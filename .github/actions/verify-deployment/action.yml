name: 'Verify ECS Deployment'
description: 'Verifies that an ECS deployment has completed successfully'

inputs:
  image-uri:
    description: 'The URI of the Docker image that was deployed'
    required: true
  service-name:
    description: 'The name of the ECS service'
    required: true
  cluster:
    description: 'The name of the ECS cluster'
    required: true
  timeout:
    description: 'Timeout in seconds for the verification'
    required: false
    default: '600'

runs:
  using: 'composite'
  steps:
    - name: Verify Deployment
      shell: bash
      run: |
        IMAGE_URI=${{ inputs.image-uri }}
        SERVICE_NAME=${{ inputs.service-name }}
        ECS_CLUSTER=${{ inputs.cluster }}
        TIMEOUT=${{ inputs.timeout }}

        echo "Starting deployment verification for service $SERVICE_NAME in cluster $ECS_CLUSTER"

        LATEST_TASK_DEF=$(aws ecs list-task-definitions --family-prefix $SERVICE_NAME --sort DESC --status ACTIVE --query "taskDefinitionArns[0]" --output text | head -n 1)

        echo "New active task definition: $LATEST_TASK_DEF"

        check_tasks() {
          echo "----------------------------------------------------------------------------------------------------"
          echo "$(date): Checking all running tasks..."

          TASKS=$(aws ecs list-tasks --cluster $ECS_CLUSTER --service-name $SERVICE_NAME --output json)
          TASK_ARNS=$(echo $TASKS | jq -r '.taskArns[]')

          if [ -z "$TASK_ARNS" ]; then
            echo "No running tasks found."
            return 1
          fi
          
          ALL_TASKS_UPDATED=0

          for TASK_ARN in $TASK_ARNS; do
            TASK_INFO=$(aws ecs describe-tasks --cluster $ECS_CLUSTER --tasks $TASK_ARN --output json)
          
            TASK_DEF=$(echo $TASK_INFO | jq -r '.tasks[0].taskDefinitionArn')
            TASK_STATUS=$(echo $TASK_INFO | jq -r '.tasks[0].lastStatus')
          
            RUNNING_IMAGE=$(echo $TASK_INFO | jq -r --arg SERVICE_NAME "$SERVICE_NAME" '.tasks[0].containers[] | select(.name == $SERVICE_NAME).image')

            SIMPLIFIED_TASK_DEF=$(echo $TASK_DEF | sed 's/.*task-definition\///')
          
            echo "$TASK_ARN ($SIMPLIFIED_TASK_DEF) ($TASK_STATUS)"
          
            if [ "$TASK_DEF" != "$LATEST_TASK_DEF" ]; then
              ALL_TASKS_UPDATED=1
            fi
          done
          
          return $ALL_TASKS_UPDATED
        }

        START_TIME=$(date +%s)

        while true; do
        if check_tasks; then
          echo "Deployment completed. All tasks are now running the expected image: $IMAGE_URI"
          exit 0
        fi

        CURRENT_TIME=$(date +%s)
        ELAPSED_TIME=$((CURRENT_TIME - START_TIME))

        if [ $ELAPSED_TIME -gt $TIMEOUT ]; then
          echo "Timeout reached. Not all tasks are running the expected image."
          exit 1
        fi

        sleep 15
        done
