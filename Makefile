.PHONY: infra/raise infra/down test/all test/commons test/math-agent test/web-search-agent test/orchestrator-agent test/magie-wrapper

infra/raise:
	docker compose up -d
	# The DATABASE_URL for the postgres service in docker-compose.yaml would be:
	# DATABASE_URL=postgresql://myuser:mypassword@localhost:5432/mydatabase

infra/down:
	docker compose down

# Run all tests across all projects
test/all:
	@echo "🧪 Running all tests across all projects..."
	@echo "=========================================="
	$(MAKE) -C commons test
	$(MAKE) -C math-agent test
	$(MAKE) -C web-search-agent test
	$(MAKE) -C magie-wrapper test
	@echo "=========================================="
	@echo "✅ All tests completed!"

# Run tests for commons project
test/commons:
	@echo "📦 Testing commons..."
	$(MAKE) -C commons test

# Run tests for math-agent project
test/math-agent:
	@echo "🧮 Testing math-agent..."
	$(MAKE) -C math-agent test

# Run tests for web-search-agent project
test/web-search-agent:
	@echo "🔍 Testing web-search-agent..."
	$(MAKE) -C web-search-agent test

# Run tests for magie-wrapper project
test/magie-wrapper:
	@echo "🎭 Testing magie-wrapper..."
	$(MAKE) -C magie-wrapper test

# Run tests for orchestrator-agent project
test/orchestrator-agent:
	@echo "🎯 Testing orchestrator-agent..."
	$(MAKE) -C orchestrator-agent test