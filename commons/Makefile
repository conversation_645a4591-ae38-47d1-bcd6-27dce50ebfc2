.PHONY: help install test test-cov lint clean

# Default target
help:
	@echo "Available commands:"
	@echo "  install   - Install dependencies"
	@echo "  test      - Run tests"
	@echo "  test-cov  - Run tests with coverage"
	@echo "  lint      - Run linting"
	@echo "  clean     - Clean up generated files"

# Install dependencies
install:
	uv sync

# Run tests
test:
	uv run pytest tests/ -v

# Run tests with coverage
test-cov:
	uv run pytest tests/ --cov=commons --cov-report=term-missing

# Run linting (placeholder - add actual linting tool when needed)
lint:
	@echo "Linting not configured yet"

# Clean up generated files
clean:
	rm -rf .pytest_cache/
	rm -rf __pycache__/
	rm -rf src/__pycache__/
	rm -rf src/commons/__pycache__/
	rm -rf tests/__pycache__/
	rm -rf tests/unit/__pycache__/
	rm -rf tests/unit/commons/__pycache__/
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete 