# Commons

Shared utilities and common functionality for the Magie agents project.

## Installation

```bash
uv sync
```

## Usage

```python
from commons.observability import get_langfuse_client

# Get Langfuse client for observability
langfuse = get_langfuse_client()
```

## Testing

```bash
# Run all tests
make test

# Run tests with coverage
make test-cov

# Run specific test file
uv run pytest tests/unit/commons/test_observaility.py -v
```

## Development

```bash
# Install dependencies
make install

# Run linting
make lint

# Run tests
make test
```
