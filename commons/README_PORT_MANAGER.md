# Port Manager

The `PortManager` class provides centralized port management for agent services to prevent port conflicts.

## Overview

The `PortManager` is a utility class that ensures each agent type gets a unique port assignment. It's designed to prevent port conflicts when running multiple agent services simultaneously.

## Current Port Assignments

| Agent Type | Port |
|------------|------|
| Math Agent | 10002 |
| Magie Wrapper | 10003 |
| Web Search Agent | 10004 |
| Orchestrator Agent | 10001 |

## Usage

### Basic Usage

```python
from commons.port_manager import PortManager, AgentType

# Get port for a specific agent type
port = PortManager.get_port(AgentType.MATH_AGENT)
print(port)  # Output: 10002

# Get port by agent name (case-insensitive)
port = PortManager.get_port_by_name("math_agent")
print(port)  # Output: 10002
```

### Convenience Functions

For easier access, convenience functions are provided:

```python
from commons.port_manager import (
    get_math_agent_port,
    get_magie_wrapper_port,
    get_web_search_agent_port,
    get_orchestrator_agent_port
)

math_port = get_math_agent_port()      # 10002
magie_port = get_magie_wrapper_port()  # 10003
web_port = get_web_search_agent_port() # 10004
orch_port = get_orchestrator_agent_port() # 10001
```

### Advanced Usage

```python
from commons.port_manager import PortManager, AgentType

# Check if a port is available
is_available = PortManager.is_port_available(10005)  # True

# Get the next available port
next_port = PortManager.get_next_available_port()  # 10005

# Get all current assignments
assignments = PortManager.get_all_assignments()
# Returns: {
#     "math_agent": 10002,
#     "magie_wrapper": 10003,
#     "web_search_agent": 10004,
#     "orchestrator_agent": 10001
# }

# Validate that all assignments are unique
is_valid = PortManager.validate_assignments()  # True
```

### Registering New Agents

```python
from commons.port_manager import PortManager

# Create a custom agent type
class CustomAgentType:
    value = "custom_agent"

# Register with a specific port
PortManager.register_agent(CustomAgentType(), 10005)

# Get the port
port = PortManager.get_port(CustomAgentType())  # 10005
```

## Integration with Agent Services

The PortManager has been integrated into the existing agent services:

### Before (Hardcoded)
```python
def main():
    host = "0.0.0.0"
    port = 10003  # Hardcoded
    # ... rest of the code
```

### After (Using PortManager)
```python
from commons.port_manager import get_magie_wrapper_port

def main():
    host = "0.0.0.0"
    port = get_magie_wrapper_port()  # Centralized
    # ... rest of the code
```

## Benefits

1. **No Port Conflicts**: Ensures each agent gets a unique port
2. **Centralized Management**: All port assignments are in one place
3. **Easy to Extend**: Simple to add new agent types
4. **Type Safety**: Uses enums for agent types
5. **Validation**: Built-in validation to prevent duplicate assignments

## Testing

The PortManager includes comprehensive tests covering:

- Port assignment retrieval
- Agent name lookup (case-insensitive)
- Port availability checking
- Next available port calculation
- Agent registration
- Conflict detection
- Assignment validation

Run the tests with:
```bash
cd commons
uv run pytest tests/unit/commons/test_port_manager.py -v
```