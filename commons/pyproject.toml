[project]
name = "commons"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "~=3.13.0"
dependencies = [
    "langfuse>=3.2.1",
    "python-dotenv~=1.1.1",
    "google-adk~=1.12.0",
    "python-json-logger>=2.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=8.4.1",
    "pytest-mock>=3.12.0",
    "pytest-cov>=5.0.0",
]
