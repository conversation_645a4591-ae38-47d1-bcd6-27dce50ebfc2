import logging
import os

from google.adk.memory import InMemoryMemoryService, VertexAiRagMemoryService
from google.adk.sessions import InMemorySessionService
from google.adk.sessions.database_session_service import DatabaseSessionService


def get_default_db_config():
    return {
        "pool_size": 10,  # Maintain 10 connections in the pool
        "max_overflow": 10,  # Allow up to 10 additional connections to be created if needed
        "pool_recycle": 3600,  # Recycle connections after 1 hour (3600 seconds)
        "pool_use_lifo": True,  # Use LIFO (Last In First Out) order for connection pooling
        "pool_pre_ping": True,  # Pre-ping the database to check if the connection is still valid
    }


def get_session_service(logger: logging.Logger = None):
    if not logger:
        logger = logging.getLogger(__name__)
        logger.info("No logger provided, using default logger")

    session_service_type = os.getenv("SESSION_SERVICE_TYPE", "in_memory")
    logger.info(f"Initializing session service: {session_service_type}")
    match session_service_type:
        case "in_memory":
            return InMemorySessionService()
        case "database":
            return DatabaseSessionService(
                os.getenv("DATABASE_URL"), **get_default_db_config()
            )
        case _:
            logger.error(f"Invalid session service: {session_service_type}")


def get_memory_service(logger: logging.Logger = None):
    if not logger:
        logger = logging.getLogger(__name__)
        logger.info("No logger provided, using default logger")

    memory_service_type = os.getenv("MEMORY_SERVICE_TYPE", "in_memory")
    logger.info(f"Initializing memory service: {memory_service_type}")
    match memory_service_type:
        case "in_memory":
            return InMemoryMemoryService()
        case "vertex_ai":
            return VertexAiRagMemoryService()
        case _:
            logger.error(f"Invalid memory service: {memory_service_type}")
            raise ValueError(f"Invalid memory service: {memory_service_type}")
