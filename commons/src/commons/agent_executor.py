import logging
from contextlib import suppress
from collections.abc import AsyncGenerator

from a2a.server.agent_execution import Agent<PERSON>xecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.server.tasks import TaskUpdater
from a2a.types import TaskState, UnsupportedOperationError
from a2a.utils.errors import ServerError
from google.adk import Runner
from google.adk.events import Event
from google.genai import types

from commons.a2a import convert_a2a_parts_to_genai, convert_genai_parts_to_a2a

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class RunnerAgentExecutor(AgentExecutor):
    """An AgentExecutor that runs the Math ADK-based Agent."""

    def __init__(self, runner: Runner, user_id: str = "runner_agent"):
        self.runner = runner
        self.user_id = user_id
        self._running_sessions = {}

    def _run_agent(
        self, session_id, new_message: types.Content
    ) -> AsyncGenerator[Event, None]:
        return self.runner.run_async(
            session_id=session_id, user_id=self.user_id, new_message=new_message
        )

    async def _process_request(
        self,
        new_message: types.Content,
        session_id: str,
        task_updater: TaskUpdater,
    ) -> None:
        session_obj = await self._upsert_session(session_id)
        session_id = session_obj.id

        agent_generator = self._run_agent(session_id, new_message)
        try:
            async for event in agent_generator:
                if event.is_final_response():
                    parts = convert_genai_parts_to_a2a(
                        event.content.parts if event.content and event.content.parts else []
                    )
                    logger.debug("Final response: %s", parts)
                    await task_updater.add_artifact(parts)
                    await task_updater.complete()
                    # close the stream explicitly before exiting
                    with suppress(Exception):
                        await agent_generator.aclose()
                    break

                if not event.get_function_calls():
                    await task_updater.update_status(
                        TaskState.working,
                        message=task_updater.new_agent_message(
                            convert_genai_parts_to_a2a(
                                event.content.parts
                                if event.content and event.content.parts
                                else []
                            ),
                        ),
                    )
                else:
                    logger.debug("Skipping event")
        finally:
            # guarantee closure in case of exception/cancellation
            with suppress(Exception):
                await agent_generator.aclose()

    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ):
        if not context.task_id or not context.context_id:
            raise ValueError("RequestContext must have task_id and context_id")
        if not context.message:
            raise ValueError("RequestContext must have a message")

        updater = TaskUpdater(event_queue, context.task_id, context.context_id)
        if not context.current_task:
            await updater.submit()
        await updater.start_work()

        await self._process_request(
            types.UserContent(
                parts=convert_a2a_parts_to_genai(context.message.parts),
            ),
            context.context_id,
            updater,
        )

    async def cancel(self, context: RequestContext, event_queue: EventQueue):
        raise ServerError(error=UnsupportedOperationError())

    async def _upsert_session(self, session_id: str):
        session = await self.runner.session_service.get_session(
            app_name=self.runner.app_name, user_id=self.user_id, session_id=session_id
        )
        if session is None:
            session = await self.runner.session_service.create_session(
                app_name=self.runner.app_name,
                user_id=self.user_id,
                session_id=session_id,
            )
        if session is None:
            raise RuntimeError(f"Failed to get or create session: {session_id}")
        return session
