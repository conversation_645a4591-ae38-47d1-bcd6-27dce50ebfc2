import json
import logging
import os

from commons.log_formatter import setup_json_logging

logger = setup_json_logging("commons.infra", logging.INFO)

def setup_google_credentials():
    """ Sets up Google credentials by creating the secrets directory and writing credentials if available."""
    google_app_credentials = os.getenv("GOOGLE_APPLICATION_CREDENTIALS_JSON")
    if google_app_credentials:
        try:
            json.loads(google_app_credentials)
            os.makedirs("/app/secrets", exist_ok=True)
            credentials_filepath = "/app/secrets/agent-server-key.json"
            with open(credentials_filepath, "w") as f:
                f.write(google_app_credentials)
            os.chmod(credentials_filepath, 0o600)
            logger.info("agent-server-key.json written to %s", credentials_filepath)
        except (json.J<PERSON><PERSON><PERSON>ode<PERSON><PERSON><PERSON>, OSError, IOError) as e:
            logger.error("Failed to setup Google credentials: %s", e)
            raise
    else:
        logger.warning("GOOGLE_APPLICATION_CREDENTIALS_JSON is empty or undefined")
