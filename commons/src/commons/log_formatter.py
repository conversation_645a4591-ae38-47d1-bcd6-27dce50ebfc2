import logging
from pythonjsonlogger import jsonlogger


RESERVED_ATTRS = list(jsonlogger.RESERVED_ATTRS) + ["color_message"]


def setup_json_logging(logger_name: str = None, level: int = logging.INFO):
    """
    Setup a logger with JSON formatting using python-json-logger.
    
    Args:
        logger_name: Name of the logger. If None, uses root logger
        level: Logging level (default: INFO)
    
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)

    handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt='%(asctime)s %(levelname)s %(message)s %(module)s %(lineno)d',
        datefmt='%Y-%m-%d %H:%M:%S',
        reserved_attrs=RESERVED_ATTRS
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger


def get_uvicorn_log_config(level="INFO"):
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(levelname)s %(message)s %(module)s %(lineno)d",
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "reserved_attrs": RESERVED_ATTRS
            },
            "access": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(levelname)s %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "reserved_attrs": RESERVED_ATTRS
            },
        },
        "handlers": {
            "default": {
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
                "formatter": "default",
            },
            "access": {
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
                "formatter": "access",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["default"], "level": level, "propagate": False},
            "uvicorn.error": {"handlers": ["default"], "level": level, "propagate": False},
            "uvicorn.access": {"handlers": ["access"], "level": level, "propagate": False},
            "google_adk": {"handlers": ["default"], "level": level, "propagate": False},
        },
        "root": {"level": level, "handlers": ["default"]},
    }
