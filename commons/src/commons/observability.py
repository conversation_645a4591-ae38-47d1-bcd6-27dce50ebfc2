import base64
import os

from dotenv import load_dotenv
from langfuse import Langfuse, get_client


def get_langfuse_client() -> Langfuse:
    # Load environment variables from .env file
    load_dotenv()

    # Build Basic Auth header.
    LANGFUSE_AUTH = base64.b64encode(
        f"{os.environ.get('LANGFUSE_PUBLIC_KEY')}:{os.environ.get('LANGFUSE_SECRET_KEY')}".encode()
    ).decode()

    # Configure OpenTelemetry endpoint & headers
    os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = (
        os.environ.get("LANGFUSE_HOST") + "/api/public/otel"
    )
    os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"Authorization=Basic {LANGFUSE_AUTH}"

    langfuse = get_client()
    if langfuse.auth_check():
        return langfuse
    else:
        raise Exception(
            "Authentication failed. Please check your credentials and host."
        )


if __name__ == "__main__":
    # langfuse = get_langfuse_client()
    # print(langfuse.auth_check())
    from langfuse import Langfuse

    langfuse = get_langfuse_client()

    print(langfuse.auth_check())
