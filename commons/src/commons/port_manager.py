"""
Port management utilities for agent services.

This module provides a centralized way to manage port assignments
for different agent services to prevent port conflicts.
"""

from enum import Enum
from typing import Dict, Optional


class AgentType(Enum):
    """Enumeration of agent types with their assigned ports."""
    
    MATH_AGENT = "math_agent"
    MAGIE_WRAPPER = "magie_wrapper"
    WEB_SEARCH_AGENT = "web_search_agent"
    ORCHESTRATOR_AGENT = "orchestrator_agent"


class PortManager:
    """
    Centralized port management for agent services.
    
    This class ensures that each agent type gets a unique port
    and provides methods to retrieve and validate port assignments.
    """
    
    # Port assignments for each agent type
    _PORT_ASSIGNMENTS: Dict[AgentType, int] = {
        AgentType.MATH_AGENT: 10002,
        AgentType.MAGIE_WRAPPER: 10003,
        AgentType.WEB_SEARCH_AGENT: 10004,
        AgentType.ORCHESTRATOR_AGENT: 10001,  # Reserved for future use
    }
    
    @classmethod
    def get_port(cls, agent_type: AgentType) -> int:
        """
        Get the assigned port for a specific agent type.
        
        Args:
            agent_type: The type of agent requesting a port
            
        Returns:
            The assigned port number for the agent type
            
        Raises:
            ValueError: If the agent type is not registered
        """
        if agent_type not in cls._PORT_ASSIGNMENTS:
            raise ValueError(f"Agent type '{agent_type.value}' is not registered for port assignment")
        
        return cls._PORT_ASSIGNMENTS[agent_type]
    
    @classmethod
    def get_port_by_name(cls, agent_name: str) -> int:
        """
        Get the assigned port for an agent by its name.
        
        Args:
            agent_name: The name of the agent (case-insensitive)
            
        Returns:
            The assigned port number for the agent
            
        Raises:
            ValueError: If the agent name is not recognized
        """
        agent_name_lower = agent_name.lower()
        
        for agent_type in AgentType:
            if agent_type.value.lower() == agent_name_lower:
                return cls.get_port(agent_type)
        
        raise ValueError(f"Agent name '{agent_name}' is not recognized")
    
    @classmethod
    def register_agent(cls, agent_type: AgentType, port: int) -> None:
        """
        Register a new agent type with a specific port.
        
        Args:
            agent_type: The type of agent to register
            port: The port number to assign
            
        Raises:
            ValueError: If the port is already assigned to another agent
        """
        # Check if port is already in use
        for existing_type, existing_port in cls._PORT_ASSIGNMENTS.items():
            if existing_port == port and existing_type != agent_type:
                raise ValueError(f"Port {port} is already assigned to {existing_type.value}")
        
        cls._PORT_ASSIGNMENTS[agent_type] = port
    
    @classmethod
    def get_all_assignments(cls) -> Dict[str, int]:
        """
        Get all current port assignments.
        
        Returns:
            Dictionary mapping agent names to their assigned ports
        """
        return {agent_type.value: port for agent_type, port in cls._PORT_ASSIGNMENTS.items()}
    
    @classmethod
    def is_port_available(cls, port: int) -> bool:
        """
        Check if a port is available (not assigned to any agent).
        
        Args:
            port: The port number to check
            
        Returns:
            True if the port is available, False otherwise
        """
        return port not in cls._PORT_ASSIGNMENTS.values()
    
    @classmethod
    def get_next_available_port(cls, start_port: int = 10005) -> int:
        """
        Get the next available port starting from a given port number.
        
        Args:
            start_port: The port number to start searching from
            
        Returns:
            The next available port number
        """
        port = start_port
        while not cls.is_port_available(port):
            port += 1
        return port
    
    @classmethod
    def validate_assignments(cls) -> bool:
        """
        Validate that all port assignments are unique.
        
        Returns:
            True if all assignments are valid, False otherwise
        """
        ports = list(cls._PORT_ASSIGNMENTS.values())
        return len(ports) == len(set(ports))


# Convenience functions for easy access
def get_math_agent_port() -> int:
    """Get the port for the math agent."""
    return PortManager.get_port(AgentType.MATH_AGENT)


def get_magie_wrapper_port() -> int:
    """Get the port for the magie wrapper agent."""
    return PortManager.get_port(AgentType.MAGIE_WRAPPER)


def get_web_search_agent_port() -> int:
    """Get the port for the web search agent."""
    return PortManager.get_port(AgentType.WEB_SEARCH_AGENT)


def get_orchestrator_agent_port() -> int:
    """Get the port for the orchestrator agent."""
    return PortManager.get_port(AgentType.ORCHESTRATOR_AGENT) 