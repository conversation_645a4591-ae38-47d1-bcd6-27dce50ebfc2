# Commons Tests

This directory contains the test suite for the commons package.

## Structure

```
tests/
├── unit/                    # Unit tests
│   └── commons/            # Tests for commons module
│       └── test_observaility.py  # Tests for observability module
└── README.md               # This file
```

## Running Tests

### Prerequisites

Make sure you have the dependencies installed:

```bash
uv sync
```

### Run All Tests

```bash
uv run pytest
```

### Run Specific Test File

```bash
uv run pytest tests/unit/commons/test_observaility.py
```

### Run Tests with Coverage

```bash
uv run pytest tests/unit/commons/test_observaility.py --cov=commons.observability --cov-report=term-missing
```

### Run Tests Verbosely

```bash
uv run pytest tests/unit/commons/test_observaility.py -v
```

## Test Coverage

The observability module currently has **82% test coverage**, which includes:

- ✅ Successful Langfuse client creation
- ✅ Authentication failure scenarios
- ✅ Missing credentials handling
- ✅ Empty credentials handling
- ✅ Custom host configuration
- ✅ Special characters in credentials
- ✅ Unicode characters in credentials
- ✅ Host without protocol
- ✅ Exception handling during client creation
- ✅ Exception handling during auth check

The missing 18% coverage is the `if __name__ == "__main__":` block, which is not typically tested in unit tests.

## Test Patterns

The tests follow these patterns:

1. **Mocking**: External dependencies are mocked using `unittest.mock`
2. **Arrange-Act-Assert**: Clear test structure with setup, execution, and verification
3. **Edge Cases**: Tests cover various edge cases and error conditions
4. **Descriptive Names**: Test method names clearly describe what is being tested
5. **Documentation**: Each test has a docstring explaining its purpose 