import json
import unittest
from unittest.mock import patch, mock_open, MagicMock

from commons.infra import setup_google_credentials


class TestInfra(unittest.TestCase):

    @patch("os.getenv")
    @patch("os.makedirs")
    @patch("builtins.open", new_callable=mock_open)
    @patch("json.loads")
    @patch("os.chmod")
    @patch("commons.infra.logger")
    def test_setup_google_credentials_with_env_var(self, mock_logger, mock_chmod, mock_json_loads, 
                                                  mock_file, mock_makedirs, mock_getenv):
        # Arrange
        mock_getenv.return_value = '{"key": "value"}'
        
        # Act
        setup_google_credentials()
        
        # Assert
        mock_getenv.assert_called_once_with("GOOGLE_APPLICATION_CREDENTIALS_JSON")
        mock_json_loads.assert_called_once_with('{"key": "value"}')
        mock_makedirs.assert_called_once_with("/app/secrets", exist_ok=True)
        mock_file.assert_called_once_with("/app/secrets/agent-server-key.json", "w")
        mock_file().write.assert_called_once_with('{"key": "value"}')
        mock_chmod.assert_called_once_with("/app/secrets/agent-server-key.json", 0o600)
        mock_logger.info.assert_called_once()

    @patch("os.getenv")
    @patch("os.makedirs")
    @patch("builtins.open", new_callable=mock_open)
    @patch("commons.infra.logger")
    def test_setup_google_credentials_without_env_var(self, mock_logger, mock_file, mock_makedirs, mock_getenv):
        # Arrange
        mock_getenv.return_value = None
        
        # Act
        setup_google_credentials()
        
        # Assert
        mock_getenv.assert_called_once_with("GOOGLE_APPLICATION_CREDENTIALS_JSON")
        mock_makedirs.assert_not_called()
        mock_file.assert_not_called()
        mock_logger.warning.assert_called_once()
        
    @patch("os.getenv")
    @patch("json.loads")
    @patch("commons.infra.logger")
    def test_setup_google_credentials_with_invalid_json(self, mock_logger, mock_json_loads, mock_getenv):
        # Arrange
        mock_getenv.return_value = "invalid-json"
        mock_json_loads.side_effect = json.JSONDecodeError("Invalid JSON", "invalid-json", 0)
        
        # Act & Assert
        with self.assertRaises((json.JSONDecodeError, OSError, IOError)):
            setup_google_credentials()
            
        mock_logger.error.assert_called_once()


if __name__ == "__main__":
    unittest.main()
