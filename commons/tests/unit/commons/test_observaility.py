import base64
from unittest.mock import Mock, patch

import pytest
from langfuse import Lang<PERSON>

from commons.observability import get_langfuse_client


class TestObservability:
    """Test cases for the observability module."""

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_success(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test successful Langfuse client creation."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = True
        mock_get_client.return_value = mock_langfuse

        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "test_public_key",
            "LANGFUSE_SECRET_KEY": "test_secret_key",
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        # Act
        result = get_langfuse_client()

        # Assert
        mock_load_dotenv.assert_called_once()
        mock_get_client.assert_called_once()
        mock_langfuse.auth_check.assert_called_once()
        assert result == mock_langfuse

        # Verify environment variables were set correctly
        expected_auth = base64.b64encode(b"test_public_key:test_secret_key").decode()
        mock_environ.__setitem__.assert_any_call(
            "OTEL_EXPORTER_OTLP_ENDPOINT", "https://test.langfuse.com/api/public/otel"
        )
        mock_environ.__setitem__.assert_any_call(
            "OTEL_EXPORTER_OTLP_HEADERS", f"Authorization=Basic {expected_auth}"
        )

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_auth_failure(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with authentication failure."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = False
        mock_get_client.return_value = mock_langfuse

        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "test_public_key",
            "LANGFUSE_SECRET_KEY": "test_secret_key",
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        # Act & Assert
        with pytest.raises(
            Exception,
            match="Authentication failed. Please check your credentials and host.",
        ):
            get_langfuse_client()

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_missing_credentials(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with missing credentials."""
        # Arrange
        mock_environ.get.side_effect = lambda key, default=None: None

        # Act & Assert
        with pytest.raises(
            TypeError,
            match="unsupported operand type\\(s\\) for \\+: 'NoneType' and 'str'",
        ):
            get_langfuse_client()

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_empty_credentials(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with empty credentials."""
        # Arrange
        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "",
            "LANGFUSE_SECRET_KEY": "",
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = False
        mock_get_client.return_value = mock_langfuse

        # Act & Assert
        with pytest.raises(
            Exception,
            match="Authentication failed. Please check your credentials and host.",
        ):
            get_langfuse_client()

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_custom_host(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with custom host."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = True
        mock_get_client.return_value = mock_langfuse

        custom_host = "https://custom.langfuse.com"
        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "test_public_key",
            "LANGFUSE_SECRET_KEY": "test_secret_key",
            "LANGFUSE_HOST": custom_host,
        }.get(key, default)

        # Act
        result = get_langfuse_client()

        # Assert
        mock_environ.__setitem__.assert_any_call(
            "OTEL_EXPORTER_OTLP_ENDPOINT", f"{custom_host}/api/public/otel"
        )
        assert result == mock_langfuse

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_special_characters_in_credentials(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with special characters in credentials."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = True
        mock_get_client.return_value = mock_langfuse

        special_public_key = "test@public:key"
        special_secret_key = "test#secret$key"

        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": special_public_key,
            "LANGFUSE_SECRET_KEY": special_secret_key,
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        # Act
        result = get_langfuse_client()

        # Assert
        expected_auth = base64.b64encode(
            f"{special_public_key}:{special_secret_key}".encode()
        ).decode()
        mock_environ.__setitem__.assert_any_call(
            "OTEL_EXPORTER_OTLP_HEADERS", f"Authorization=Basic {expected_auth}"
        )
        assert result == mock_langfuse

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_unicode_credentials(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with unicode characters in credentials."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = True
        mock_get_client.return_value = mock_langfuse

        unicode_public_key = "test_public_🔑"
        unicode_secret_key = "test_secret_🔐"

        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": unicode_public_key,
            "LANGFUSE_SECRET_KEY": unicode_secret_key,
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        # Act
        result = get_langfuse_client()

        # Assert
        expected_auth = base64.b64encode(
            f"{unicode_public_key}:{unicode_secret_key}".encode()
        ).decode()
        mock_environ.__setitem__.assert_any_call(
            "OTEL_EXPORTER_OTLP_HEADERS", f"Authorization=Basic {expected_auth}"
        )
        assert result == mock_langfuse

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_host_without_protocol(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation with host without protocol."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.return_value = True
        mock_get_client.return_value = mock_langfuse

        host_without_protocol = "test.langfuse.com"
        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "test_public_key",
            "LANGFUSE_SECRET_KEY": "test_secret_key",
            "LANGFUSE_HOST": host_without_protocol,
        }.get(key, default)

        # Act
        result = get_langfuse_client()

        # Assert
        mock_environ.__setitem__.assert_any_call(
            "OTEL_EXPORTER_OTLP_ENDPOINT", f"{host_without_protocol}/api/public/otel"
        )
        assert result == mock_langfuse

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_exception_handling(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation when get_client raises an exception."""
        # Arrange
        mock_get_client.side_effect = Exception("Connection failed")

        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "test_public_key",
            "LANGFUSE_SECRET_KEY": "test_secret_key",
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        # Act & Assert
        with pytest.raises(Exception, match="Connection failed"):
            get_langfuse_client()

    @patch("commons.observability.load_dotenv")
    @patch("commons.observability.get_client")
    @patch("commons.observability.os.environ")
    def test_get_langfuse_client_auth_check_exception(
        self, mock_environ, mock_get_client, mock_load_dotenv
    ):
        """Test Langfuse client creation when auth_check raises an exception."""
        # Arrange
        mock_langfuse = Mock(spec=Langfuse)
        mock_langfuse.auth_check.side_effect = Exception("Auth check failed")
        mock_get_client.return_value = mock_langfuse

        mock_environ.get.side_effect = lambda key, default=None: {
            "LANGFUSE_PUBLIC_KEY": "test_public_key",
            "LANGFUSE_SECRET_KEY": "test_secret_key",
            "LANGFUSE_HOST": "https://test.langfuse.com",
        }.get(key, default)

        # Act & Assert
        with pytest.raises(Exception, match="Auth check failed"):
            get_langfuse_client()
