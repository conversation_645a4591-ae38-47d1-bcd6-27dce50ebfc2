"""
Unit tests for the PortManager class.
"""

import pytest

from commons.port_manager import (
    AgentType,
    PortManager,
    get_math_agent_port,
    get_magie_wrapper_port,
    get_orchestrator_agent_port,
    get_web_search_agent_port,
)


class TestAgentType:
    """Test cases for the AgentType enum."""
    
    def test_agent_type_values(self):
        """Test that all agent types have the expected values."""
        assert AgentType.MATH_AGENT.value == "math_agent"
        assert AgentType.MAGIE_WRAPPER.value == "magie_wrapper"
        assert AgentType.WEB_SEARCH_AGENT.value == "web_search_agent"
        assert AgentType.ORCHESTRATOR_AGENT.value == "orchestrator_agent"
    
    def test_agent_type_enumeration(self):
        """Test that all agent types can be enumerated."""
        agent_types = list(AgentType)
        assert len(agent_types) == 4
        assert AgentType.MATH_AGENT in agent_types
        assert AgentType.MAGIE_WRAPPER in agent_types
        assert AgentType.WEB_SEARCH_AGENT in agent_types
        assert AgentType.ORCHESTRATOR_AGENT in agent_types


class TestPortManager:
    """Test cases for the PortManager class."""
    
    def setup_method(self):
        """Reset port assignments before each test."""
        # Reset to original assignments
        PortManager._PORT_ASSIGNMENTS = {
            AgentType.MATH_AGENT: 10002,
            AgentType.MAGIE_WRAPPER: 10003,
            AgentType.WEB_SEARCH_AGENT: 10004,
            AgentType.ORCHESTRATOR_AGENT: 10001,
        }
    
    def test_get_port_for_math_agent(self):
        """Test getting port for math agent."""
        port = PortManager.get_port(AgentType.MATH_AGENT)
        assert port == 10002
    
    def test_get_port_for_magie_wrapper(self):
        """Test getting port for magie wrapper."""
        port = PortManager.get_port(AgentType.MAGIE_WRAPPER)
        assert port == 10003
    
    def test_get_port_for_web_search_agent(self):
        """Test getting port for web search agent."""
        port = PortManager.get_port(AgentType.WEB_SEARCH_AGENT)
        assert port == 10004
    
    def test_get_port_for_orchestrator_agent(self):
        """Test getting port for orchestrator agent."""
        port = PortManager.get_port(AgentType.ORCHESTRATOR_AGENT)
        assert port == 10001
    
    def test_get_port_for_invalid_agent_type(self):
        """Test that getting port for invalid agent type raises ValueError."""
        # Create a mock agent type that's not in the enum
        class MockAgentType:
            value = "invalid_agent"
        
        with pytest.raises(ValueError, match="Agent type 'invalid_agent' is not registered"):
            PortManager.get_port(MockAgentType())
    
    def test_get_port_by_name_math_agent(self):
        """Test getting port by agent name for math agent."""
        port = PortManager.get_port_by_name("math_agent")
        assert port == 10002
    
    def test_get_port_by_name_magie_wrapper(self):
        """Test getting port by agent name for magie wrapper."""
        port = PortManager.get_port_by_name("magie_wrapper")
        assert port == 10003
    
    def test_get_port_by_name_web_search_agent(self):
        """Test getting port by agent name for web search agent."""
        port = PortManager.get_port_by_name("web_search_agent")
        assert port == 10004
    
    def test_get_port_by_name_orchestrator_agent(self):
        """Test getting port by agent name for orchestrator agent."""
        port = PortManager.get_port_by_name("orchestrator_agent")
        assert port == 10001
    
    def test_get_port_by_name_case_insensitive(self):
        """Test that port lookup by name is case insensitive."""
        port1 = PortManager.get_port_by_name("MATH_AGENT")
        port2 = PortManager.get_port_by_name("math_agent")
        port3 = PortManager.get_port_by_name("Math_Agent")
        
        assert port1 == port2 == port3 == 10002
    
    def test_get_port_by_name_invalid(self):
        """Test that getting port by invalid name raises ValueError."""
        with pytest.raises(ValueError, match="Agent name 'invalid_agent' is not recognized"):
            PortManager.get_port_by_name("invalid_agent")
    
    def test_get_all_assignments(self):
        """Test getting all port assignments."""
        assignments = PortManager.get_all_assignments()
        
        expected = {
            "math_agent": 10002,
            "magie_wrapper": 10003,
            "web_search_agent": 10004,
            "orchestrator_agent": 10001,
        }
        
        assert assignments == expected
    
    def test_is_port_available(self):
        """Test checking if a port is available."""
        # Test assigned ports
        assert not PortManager.is_port_available(10001)
        assert not PortManager.is_port_available(10002)
        assert not PortManager.is_port_available(10003)
        assert not PortManager.is_port_available(10004)
        
        # Test unassigned ports
        assert PortManager.is_port_available(10005)
        assert PortManager.is_port_available(10006)
        assert PortManager.is_port_available(9999)
    
    def test_get_next_available_port(self):
        """Test getting the next available port."""
        # Should return 10005 since 10001-10004 are assigned
        next_port = PortManager.get_next_available_port()
        assert next_port == 10005
        
        # Test with custom start port that's already assigned
        next_port = PortManager.get_next_available_port(10002)
        assert next_port == 10005
        
        # Test with custom start port that's available
        next_port = PortManager.get_next_available_port(10006)
        assert next_port == 10006
    
    def test_validate_assignments(self):
        """Test that all port assignments are valid (unique)."""
        assert PortManager.validate_assignments() is True
    
    def test_register_agent_new(self):
        """Test registering a new agent type."""
        # Create a mock agent type for testing
        class MockAgentType:
            value = "test_agent"
        
        new_agent_type = MockAgentType()
        
        # Register it with a new port
        PortManager.register_agent(new_agent_type, 10005)
        
        # Verify it was registered
        port = PortManager.get_port(new_agent_type)
        assert port == 10005
        
        # Verify it appears in all assignments
        assignments = PortManager.get_all_assignments()
        assert "test_agent" in assignments
        assert assignments["test_agent"] == 10005
    
    def test_register_agent_conflict(self):
        """Test that registering an agent with an existing port raises ValueError."""
        # Create a mock agent type for testing
        class MockAgentType:
            value = "conflict_agent"
        
        new_agent_type = MockAgentType()
        
        with pytest.raises(ValueError, match="Port 10002 is already assigned to math_agent"):
            PortManager.register_agent(new_agent_type, 10002)
    
    def test_register_agent_update_existing(self):
        """Test updating an existing agent's port."""
        # Update math agent's port
        PortManager.register_agent(AgentType.MATH_AGENT, 10006)
        
        # Verify the port was updated
        port = PortManager.get_port(AgentType.MATH_AGENT)
        assert port == 10006
        
        # Verify old port is now available
        assert PortManager.is_port_available(10002)


class TestConvenienceFunctions:
    """Test cases for the convenience functions."""
    
    def setup_method(self):
        """Reset port assignments before each test."""
        # Reset to original assignments
        PortManager._PORT_ASSIGNMENTS = {
            AgentType.MATH_AGENT: 10002,
            AgentType.MAGIE_WRAPPER: 10003,
            AgentType.WEB_SEARCH_AGENT: 10004,
            AgentType.ORCHESTRATOR_AGENT: 10001,
        }
    
    def test_get_math_agent_port(self):
        """Test the get_math_agent_port convenience function."""
        port = get_math_agent_port()
        assert port == 10002
    
    def test_get_magie_wrapper_port(self):
        """Test the get_magie_wrapper_port convenience function."""
        port = get_magie_wrapper_port()
        assert port == 10003
    
    def test_get_web_search_agent_port(self):
        """Test the get_web_search_agent_port convenience function."""
        port = get_web_search_agent_port()
        assert port == 10004
    
    def test_get_orchestrator_agent_port(self):
        """Test the get_orchestrator_agent_port convenience function."""
        port = get_orchestrator_agent_port()
        assert port == 10001


class TestPortManagerIntegration:
    """Integration tests for PortManager."""
    
    def test_port_uniqueness(self):
        """Test that all assigned ports are unique."""
        ports = []
        for agent_type in AgentType:
            port = PortManager.get_port(agent_type)
            assert port not in ports, f"Port {port} is assigned to multiple agents"
            ports.append(port)
    
    def test_agent_name_uniqueness(self):
        """Test that all agent names are unique."""
        names = []
        for agent_type in AgentType:
            name = agent_type.value
            assert name not in names, f"Agent name '{name}' is duplicated"
            names.append(name)
    
    def test_port_range_validity(self):
        """Test that all ports are in a reasonable range."""
        for agent_type in AgentType:
            port = PortManager.get_port(agent_type)
            assert 1000 <= port <= 65535, f"Port {port} is outside valid range"
    
    def test_consistency_between_methods(self):
        """Test consistency between different methods of getting ports."""
        for agent_type in AgentType:
            port1 = PortManager.get_port(agent_type)
            port2 = PortManager.get_port_by_name(agent_type.value)
            assert port1 == port2, f"Port mismatch for {agent_type.value}" 