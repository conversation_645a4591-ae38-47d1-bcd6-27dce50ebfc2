version: '3.8'

services:
  db:
    image: postgres:16-alpine # Or a specific version like postgres:latest
    container_name: my_postgres_db
    environment:
      POSTGRES_DB: mydatabase
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
    ports:
      - "5432:5432" # Maps host port 5432 to container port 5432
    volumes:
      - ./data/db:/var/lib/postgresql/data # Persist data outside the container