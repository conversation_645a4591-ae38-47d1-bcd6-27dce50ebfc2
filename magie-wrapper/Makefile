.PHONY: install test test-unit test-integration lint format clean help

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install dependencies"
	@echo "  test         - Run all tests"
	@echo "  test-unit    - Run unit tests only"
	@echo "  test-http-client - Run HTTP client tests only"
	@echo "  test-agent   - Run agent tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  lint         - Run linting"
	@echo "  format       - Format code"
	@echo "  clean        - Clean up generated files"

# Install dependencies
install:
	uv sync

# Run all tests
test: test-unit test-integration test-http-client test-agent

# Run unit tests only
test-unit:
	PYTHONPATH=src:. uv run pytest tests/unit/ -v

# Run HTTP client tests only
test-http-client:
	PYTHONPATH=src:. uv run pytest tests/unit/test_http_client.py -v

# Run agent tests only
test-agent:
	PYTHONPATH=src:. uv run pytest tests/unit/test_agent.py -v

# Run integration tests only
test-integration:
	PYTHONPATH=src:. uv run pytest tests/integration/ -v

# Run tests with coverage
test-cov:
	PYTHONPATH=src:. uv run pytest --cov=magie_wrapper --cov-report=html --cov-report=term

# Lint code
lint:
	uv run ruff check .

# Format code
format:
	uv run ruff format .

# Clean up generated files
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage 