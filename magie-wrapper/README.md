# Magie Wrapper Agent

A specialized agent for calling the Magie API with a fast, optimized HTTP client implementation.

## Features

- **Fast HTTP Client**: Uses `httpx` with optimized configurations for maximum performance
- **Connection Pooling**: Reuses connections to reduce latency
- **HTTP/2 Support**: Enables HTTP/2 for better performance
- **Async/Await**: Full async support for non-blocking operations
- **Error Handling**: Comprehensive error handling and logging
- **Health Checks**: Built-in health check functionality
- **Global Client Management**: Efficient client reuse across the application

## Performance Optimizations

The HTTP client is configured with the following performance optimizations:

- **Connection Pooling**: Up to 20 keepalive connections, 100 max connections
- **Timeouts**: Optimized timeouts for fast failure and recovery
  - Connect: 5 seconds
  - Read: 30 seconds
  - Write: 10 seconds
  - Pool: 2 seconds
- **HTTP/2**: Enabled for multiplexing and better performance
- **Keepalive**: Connections kept alive for 30 seconds
- **Redirect Handling**: Limited to 5 redirects to prevent loops

## Installation

1. Install dependencies:
```bash
uv sync
```

2. Set the environment variable:
```bash
export MAGIE_API_URL="https://your-magie-api-url.com"
```

## Usage

### Basic Usage

```python
import asyncio
from magie_wrapper.http_client import MagieAPIClient
from magie_wrapper.agent import MagieWrapperAgent

# Using the HTTP client directly
async with MagieAPIClient() as client:
    result = await client.call_magie_api("What is the weather like?")
    print(result)

# Using the agent wrapper
agent = MagieWrapperAgent()
response = await agent.invoke("What is the capital of France?")
print(response)
```

### Global Client Management

```python
from magie_wrapper.http_client import get_magie_client, close_magie_client

# Get or create a global client instance
client = await get_magie_client()

# Use the client
result = await client.call_magie_api("Your query here")

# Close when done (optional, will be cleaned up automatically)
await close_magie_client()
```

### Health Check

```python
async with MagieAPIClient() as client:
    is_healthy = await client.health_check()
    if is_healthy:
        print("API is healthy")
    else:
        print("API is not responding")
```

### Concurrent Requests

```python
async with MagieAPIClient() as client:
    queries = ["Query 1", "Query 2", "Query 3"]
    tasks = [client.call_magie_api(query) for query in queries]
    results = await asyncio.gather(*tasks)
```

## Configuration

The client can be configured through environment variables:

- `MAGIE_API_URL`: The base URL for the Magie API (required)

## API Endpoints

The client expects the following API structure:

- `POST /magie-wrapper/completion` - Main endpoint to delegate the request to the Magie
- `GET /health` - Health check endpoint

### Request Format

```json
{
  "query": "Your question here",
  "additional_param": "value"
}
```

### Response Format

The client handles various response formats:

```json
{
  "response": "The answer to your question",
  "status": "success"
}
```

Or:

```json
{
  "result": "The answer to your question"
}
```

Or:

```json
{
  "answer": "The answer to your question"
}
```

## Testing

Run the test suite:

```bash
# Run all tests
make test

# Run unit tests only
make test-unit

# Run with coverage
make test-cov
```

### Test Structure

- `tests/unit/test_http_client.py` - Unit tests for the HTTP client (19 tests)
- `tests/unit/test_agent.py` - Unit tests for the agent functionality (24 tests)
- Tests cover:
  - **HTTP Client Tests:**
    - Client initialization and configuration
    - API calls with success and error scenarios
    - Health checks
    - Global client management
    - Error handling
  - **Agent Tests:**
    - Agent initialization and configuration
    - Invoke method with different response formats
    - Error handling and logging
    - Response field priority (response > result > answer > JSON)
    - Edge cases (empty queries, long queries, special characters)
    - Integration with HTTP client

## Development

### Available Make Commands

```bash
make install      # Install dependencies
make test         # Run all tests
make test-unit    # Run unit tests only
make test-cov     # Run tests with coverage
make lint         # Run linting
make format       # Format code
make clean        # Clean up generated files
```

### Running Examples

```bash
# Set the API URL
export MAGIE_API_URL="https://your-api-url.com"

# Run the example script
python examples/basic_usage.py
```

## Error Handling

The client provides comprehensive error handling:

- **HTTP Errors**: `httpx.HTTPStatusError` for 4xx/5xx responses
- **Network Errors**: `httpx.RequestError` for connection issues
- **Timeout Errors**: Automatic timeout handling
- **JSON Errors**: Proper JSON parsing with error handling

## Logging

The client uses Python's logging module with the following loggers:

- `magie_wrapper.http_client` - HTTP client operations
- `magie_wrapper.agent` - Agent operations

## Performance Considerations

1. **Connection Reuse**: The client reuses connections for better performance
2. **Concurrent Requests**: Use `asyncio.gather()` for multiple concurrent requests
3. **Global Client**: Use the global client for long-running applications
4. **Context Manager**: Use async context manager for short-lived operations

## Troubleshooting

### Common Issues

1. **Connection Errors**: Check if the API URL is correct and accessible
2. **Timeout Errors**: Adjust timeout values if needed
3. **JSON Errors**: Verify the API response format
4. **Environment Variable**: Ensure `MAGIE_API_URL` is set

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License. 