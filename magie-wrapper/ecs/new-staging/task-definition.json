{"family": "magie-wrapper", "taskRoleArn": "arn:aws:iam::992382535149:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::992382535149:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "magie-wrapper", "image": "992382535149.dkr.ecr.us-east-1.amazonaws.com/magie-wrapper:staging", "memoryReservation": 512, "memory": 512, "portMappings": [{"containerPort": 10003, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/shared/secrets-7NSTZq:DD_API_KEY::"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS_JSON", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:GOOGLE_APPLICATION_CREDENTIALS_JSON::"}, {"name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:GOOGLE_API_KEY::"}, {"name": "GOOGLE_GENAI_USE_VERTEXAI", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:GOOGLE_GENAI_USE_VERTEXAI::"}, {"name": "GOOGLE_CLOUD_PROJECT", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:GOOGLE_CLOUD_PROJECT::"}, {"name": "GOOGLE_CLOUD_LOCATION", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:GOOGLE_CLOUD_LOCATION::"}, {"name": "MAGIE_WRAPPER_URI", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:MAGIE_WRAPPER_URI::"}, {"name": "MAGIE_API_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/magie-wrapper/secrets-SvZA1L:MAGIE_API_URL::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/staging/magie-wrapper", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "new-staging"}, {"name": "DD_SERVICE", "value": "magie-wrapper"}, {"name": "DD_VERSION", "value": "1.0"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS", "value": "/app/secrets/agent-server-key.json"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"magie-wrapper\"}]", "com.datadoghq.tags.env": "staging", "com.datadoghq.tags.service": "magie-wrapper", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "512"}