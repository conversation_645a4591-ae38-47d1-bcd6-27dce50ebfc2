{"family": "magie-wrapper-prod", "taskRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "magie-wrapper-prod", "image": "836929571495.dkr.ecr.us-east-1.amazonaws.com/magie-wrapper:prod", "memoryReservation": 1024, "memory": 1024, "portMappings": [{"containerPort": 10003, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:DD_API_KEY::"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS_JSON", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:GOOGLE_APPLICATION_CREDENTIALS_JSON::"}, {"name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:GOOGLE_API_KEY::"}, {"name": "GOOGLE_GENAI_USE_VERTEXAI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:GOOGLE_GENAI_USE_VERTEXAI::"}, {"name": "GOOGLE_CLOUD_PROJECT", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:GOOGLE_CLOUD_PROJECT::"}, {"name": "GOOGLE_CLOUD_LOCATION", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:GOOGLE_CLOUD_LOCATION::"}, {"name": "MAGIE_WRAPPER_URI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:MAGIE_WRAPPER_URI::"}, {"name": "MAGIE_API_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/magie-wrapper/secrets-OLB7qe:MAGIE_API_URL::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/prod/magie-wrapper", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "prod"}, {"name": "DD_SERVICE", "value": "magie-wrapper"}, {"name": "DD_VERSION", "value": "1.0"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS", "value": "/app/secrets/agent-server-key.json"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"magie-wrapper\"}]", "com.datadoghq.tags.env": "prod", "com.datadoghq.tags.service": "magie-wrapper", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "1024"}