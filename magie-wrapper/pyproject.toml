[project]
name = "magie-wrapper"
version = "0.1.0"
description = "A specialized agent for calling the Magie API"
requires-python = "~=3.13"
dependencies = [
    "a2a-sdk~=0.3.3",
    "google-adk~=1.12.0",
    "python-dotenv~=1.1.1",
    "uvicorn~=0.35.0",
    "httpx>=0.28.1",
    "commons",
] 

[tool.uv]
dev-dependencies = [
    "h2>=4.2.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.14.1",
    "httpx>=0.28.1",
]

[tool.uv.workspace]
members = ["../commons"]

[tool.uv.sources]
commons = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/magie_wrapper"]
