import logging
import os

import uvicorn
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import <PERSON><PERSON>ultRequestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    AgentCapabilities,
    AgentCard,
    AgentSkill,
)
from dotenv import load_dotenv

from commons.observability import get_langfuse_client
from commons.port_manager import get_magie_wrapper_port
from commons.infra import setup_google_credentials
from commons.log_formatter import get_uvicorn_log_config, setup_json_logging

from magie_wrapper.agent import MagieWrapperAgentExecutor

load_dotenv()

logger = setup_json_logging("magie_wrapper", logging.INFO)


class MissingAPIKeyError(Exception):
    """Exception for missing API key."""

    pass


def main():
    """Starts the agent server."""
    host = "0.0.0.0"
    port = get_magie_wrapper_port()
    try:
        if not os.getenv("GOOGLE_GENAI_USE_VERTEXAI") == "TRUE":
            if not os.getenv("GOOGLE_API_KEY"):
                raise MissingAPIKeyError(
                    "GOOGLE_API_KEY environment variable not set and GOOGLE_GENAI_USE_VERTEXAI is not TRUE."
                )

        setup_google_credentials()

        capabilities = AgentCapabilities(streaming=True)

        skill = AgentSkill(
            id="magie_wrapper",
            name="Magie Wrapper",
            description="An agent that calls the Magie API",
            tags=["magie", "pix", "boleto"],
            examples=["Faz um pix de 1 real para o João", "Paga o boleto em aberto"],
        )

        agent_card = AgentCard(
            name="Magie Wrapper",
            description="An agent that calls the Magie API",
            url=os.getenv("MAGIE_WRAPPER_URI", f"http://localhost:{get_magie_wrapper_port()}"),
            version="1.0.0",
            defaultInputModes=["text/plain"],
            defaultOutputModes=["text/plain"],
            capabilities=capabilities,
            skills=[skill],
        )

        if os.getenv("LANGFUSE_HOST"):
            langfuse_client = get_langfuse_client()
            if langfuse_client.auth_check():
                logger.info("Langfuse client is authenticated and ready!")
            else:
                logger.error(
                    "Authentication failed. Please check your credentials and host."
                )

        agent_executor = MagieWrapperAgentExecutor()

        request_handler = DefaultRequestHandler(
            agent_executor=agent_executor,
            task_store=InMemoryTaskStore(),
        )
        server = A2AStarletteApplication(
            agent_card=agent_card, http_handler=request_handler
        )

        logger.info("Starting magie wrapper server on port %s", port)
        uvicorn.run(server.build(), host=host, port=port, access_log=False, log_config=get_uvicorn_log_config())
        
    except MissingAPIKeyError as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)


if __name__ == "__main__":
    main()
