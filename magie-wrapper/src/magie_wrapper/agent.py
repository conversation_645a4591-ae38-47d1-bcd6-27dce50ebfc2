from google.adk.agents import LlmAgent

_MAGIE_WRAPPER_INSTRUCTION = """
<System>
You are a helpful magie wrapper agent. Your job is to call the magie API and return the result.
If a user asks a question, analyze it, call the magie API and return the result.
</System>
"""


def create_agent() -> LlmAgent:
    """Constructs the ADK agent for Math."""
    return LlmAgent(
        name="magie_wrapper",
        model="gemini-2.5-flash",
        description=(   
            "Agent to call the magie API and return the result."
        ),
        instruction=_MAGIE_WRAPPER_INSTRUCTION,
        tools=[],
    )
