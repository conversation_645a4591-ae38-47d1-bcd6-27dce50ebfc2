import logging

from a2a.server.agent_execution import AgentExecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.utils import new_agent_text_message

from .http_client import get_magie_client

logger = logging.getLogger(__name__)

class MagieResponseError(Exception):
    pass

class MagieWrapperAgent:
    async def invoke(self, chat_uuid: str) -> str:
        """Invoke the Magie API with the given query.
        
        Args:
            query: The user's query to process
            
        Returns:
            The response from the Magie API
        """
        try:
            client = await get_magie_client()
            result = await client.call_magie_api(chat_uuid)
            logger.info("Response from Magie API for chatUuid: %s, result: %s", chat_uuid, result)
            
            if isinstance(result, dict):
                if 'tempContextMessages' in result:
                    return result['tempContextMessages']
                else:
                    raise MagieResponseError(f"Unexpected response from Magie API: {result}")
            else:
                return str(result)
                
        except Exception as e:
            logger.error(f"Error calling Magie API: {e}")
            return f"Error: Unable to process your request. {str(e)}"

class MagieWrapperAgentExecutor(AgentExecutor):

    def __init__(self):
        self.agent = MagieWrapperAgent()

    async def execute(self, context: RequestContext, event_queue: EventQueue):
        chat_uuid = context.message.metadata.get('userId')
        logger.info("Request for chatUuid: %s, message: %s", chat_uuid, context.message)
        result = await self.agent.invoke(chat_uuid)
        logger.info("Response for result: %s", result)
        await event_queue.enqueue_event(new_agent_text_message(result))

    async def cancel(self, context: RequestContext, event_queue: EventQueue):
        raise Exception("Cancel not supported")
