import asyncio
import logging
import os

from typing import Any, Dict, Optional
from urllib.parse import urljoin
from uuid import UUID

import httpx

logger = logging.getLogger(__name__)


class MagieAPIClient:
    """Fast HTTP client for calling the Magie API with optimized configurations."""

    def __init__(self, base_url: Optional[str] = None):
        """Initialize the Magie API client.

        Args:
            base_url: The base URL for the Magie API. If not provided,
                     will use MAGIE_API_URL environment variable.
        """
        self.base_url = base_url or os.getenv("MAGIE_API_URL")
        if not self.base_url:
            raise ValueError(
                "MAGIE_API_URL environment variable must be set or base_url must be provided"
            )

        # Remove trailing slash for consistent URL joining
        self.base_url = self.base_url.rstrip("/")

        # Configure limits for optimal performance
        limits = httpx.Limits(
            max_keepalive_connections=20,  # Keep connections alive for reuse
            max_connections=100,  # Maximum concurrent connections
            keepalive_expiry=30.0,  # Keep connections alive for 30 seconds
        )

        # Configure timeouts for fast failure and recovery
        timeout = httpx.Timeout(
            connect=5.0,  # Connection timeout
            read=30.0,  # Read timeout
            write=10.0,  # Write timeout
            pool=2.0,  # Pool timeout
        )

        # Create the HTTP client with optimized settings
        self.client = httpx.AsyncClient(
            limits=limits,
            timeout=timeout,
            headers={
                "User-Agent": "MagieWrapperAgent/1.0",
                "Accept": "application/json",
                "Content-Type": "application/json",
            },
            # Enable HTTP/2 for better performance
            http2=True,
            # Follow redirects but limit to prevent infinite loops
            follow_redirects=True,
            max_redirects=5,
        )

        logger.info(f"Initialized Magie API client with base URL: {self.base_url}")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - close the client."""
        await self.close()

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
        logger.debug("Magie API client closed")

    async def call_magie_api(self, chat_uuid: str, **kwargs) -> Dict[str, Any]:
        """Call the Magie API with the given chat_uuid.

        Args:
            chat_uuid: The chat UUID to send to the Magie API
            **kwargs: Additional parameters to pass to the API

        Returns:
            The API response as a dictionary

        Raises:
            httpx.HTTPStatusError: If the API returns an error status
            httpx.RequestError: If there's a network or connection error
        """
        url = urljoin(f"{self.base_url}", "/magie-wrapper/completion")

        payload = {"chatUuid": chat_uuid, **kwargs}

        try:
            logger.debug(f"Calling Magie API with chat_uuid: {chat_uuid}...")
            response = await self.client.post(url, json=payload)
            response.raise_for_status()

            result = response.json()
            logger.debug(f"Magie API response received successfully")
            return result

        except httpx.HTTPStatusError as e:
            logger.error(
                f"Magie API returned error status {e.response.status_code}: {e.response.text}"
            )
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error when calling Magie API: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error when calling Magie API: {e}")
            raise

    async def health_check(self) -> bool:
        """Perform a health check on the Magie API.

        Returns:
            True if the API is healthy, False otherwise
        """
        try:
            url = urljoin(f"{self.base_url}/", "health")
            response = await self.client.get(url)
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False


# Global client instance for reuse
_magie_client: Optional[MagieAPIClient] = None


async def get_magie_client() -> MagieAPIClient:
    """Get or create a global Magie API client instance.

    Returns:
        The Magie API client instance
    """
    global _magie_client
    if _magie_client is None:
        _magie_client = MagieAPIClient()
    return _magie_client


async def close_magie_client():
    """Close the global Magie API client instance."""
    global _magie_client
    if _magie_client is not None:
        await _magie_client.close()
        _magie_client = None
