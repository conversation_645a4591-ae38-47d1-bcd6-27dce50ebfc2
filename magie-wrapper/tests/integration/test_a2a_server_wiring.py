import pytest
from unittest.mock import AsyncMock, patch

import httpx

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    AgentCapabilities,
    AgentCard,
    AgentSkill,
    Message,
    MessageSendParams,
    Role,
    SendMessageRequest,
    SendMessageSuccessResponse,
    TextPart,
    Task,
)

from magie_wrapper.agent import MagieWrapperAgentExecutor


@pytest.mark.asyncio
async def test_a2a_app_routes_to_magie_executor():
    # Arrange: build minimal AgentCard like main()
    capabilities = AgentCapabilities(streaming=True)
    skill = AgentSkill(
        id="magie_wrapper",
        name="Magie Wrapper",
        description="An agent that calls the Magie API",
        tags=["magie", "pix", "boleto"],
        examples=["Faz um pix de 1 real para o João", "Paga o boleto em aberto"],
    )
    agent_card = AgentCard(
        name="Magie Wrapper",
        description="An agent that calls the Magie API",
        url="http://test",
        version="1.0.0",
        defaultInputModes=["text/plain"],
        defaultOutputModes=["text/plain"],
        capabilities=capabilities,
        skills=[skill],
    )

    # Use the real executor but mock the downstream client
    executor = MagieWrapperAgentExecutor()
    request_handler = DefaultRequestHandler(
        agent_executor=executor,
        task_store=InMemoryTaskStore(),
    )
    app = A2AStarletteApplication(agent_card=agent_card, http_handler=request_handler).build()

    user_text = "Qual é o tempo hoje?"
    message = Message(
        messageId="msg-1", 
        role=Role.user, 
        parts=[TextPart(text=user_text)],
        metadata={"userId": "test-user-a2a"}
    )
    params = MessageSendParams(message=message)
    req = SendMessageRequest(id="req-1", params=params)

    with patch("magie_wrapper.agent.get_magie_client") as mock_get_client:
        mock_client = AsyncMock()
        mock_client.call_magie_api = AsyncMock(return_value={"tempContextMessages": "Está ensolarado"})
        mock_get_client.return_value = mock_client

        transport = httpx.ASGITransport(app=app)
        async with httpx.AsyncClient(transport=transport, base_url="http://test") as client:
            # Act: call the A2A send_message endpoint via client
            from a2a.client import A2AClient

            a2a_client = A2AClient(client, agent_card, url="http://test")
            resp = await a2a_client.send_message(req)

    # Assert
    assert isinstance(resp.root, SendMessageSuccessResponse)
    # Ensure an artifact text came back from our mocked invoke path
    result = resp.root.result
    assert result is not None
    if isinstance(result, Task):
        assert result.artifacts is not None and len(result.artifacts) >= 1
        all_parts = []
        for art in result.artifacts:
            if art.parts:
                all_parts.extend(art.parts)
        assert any(getattr(p.root, "text", None) == "Está ensolarado" for p in all_parts)
    else:
        # Some handlers may return a Message; validate its text part
        assert isinstance(result, Message)
        assert result.parts and any(getattr(p.root, "text", None) == "Está ensolarado" for p in result.parts)


