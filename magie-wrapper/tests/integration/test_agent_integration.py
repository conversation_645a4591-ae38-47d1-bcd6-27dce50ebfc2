import pytest
from unittest.mock import AsyncMock, patch
import logging

from magie_wrapper.agent import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MagieWrapperAgentExecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue


class TestAgentIntegration:
    """Integration tests for agent functionality."""
    
    @pytest.fixture
    def agent(self):
        """Fixture providing a MagieWrapperAgent instance."""
        return MagieWrapperAgent()
    
    @pytest.fixture
    def executor(self):
        """Fixture providing a MagieWrapperAgentExecutor instance."""
        return MagieWrapperAgentExecutor()
    
    @pytest.mark.asyncio
    async def test_agent_with_real_client_mock(self, agent):
        """Test agent with mocked client that simulates real behavior."""
        query = "What is the weather like?"
        
        # Mock the entire client creation and API call
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            # Create a more realistic mock client
            mock_client = AsyncMock()
            mock_client.call_magie_api = AsyncMock()
            mock_client.call_magie_api.return_value = {
                "tempContextMessages": "The weather is sunny and 25°C",
                "status": "success",
                "timestamp": "2024-01-01T12:00:00Z"
            }
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "The weather is sunny and 25°C"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_agent_error_logging(self, agent, caplog):
        """Test that errors are properly logged."""
        query = "What is the weather like?"
        error_message = "API connection timeout"
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.side_effect = Exception(error_message)
            mock_get_client.return_value = mock_client
            
            with caplog.at_level(logging.ERROR):
                result = await agent.invoke(query)
            
            # Check that error was logged
            assert any(error_message in record.message for record in caplog.records)
            assert "Error calling Magie API" in caplog.text
            
            # Check that error was returned to user
            assert "Error: Unable to process your request" in result
            assert error_message in result
    
    @pytest.mark.asyncio
    async def test_agent_tempContextMessages_handling(self, agent):
        """Test that agent correctly handles tempContextMessages field."""
        test_cases = [
            # (api_response, expected_result, description)
            (
                {"tempContextMessages": "Success response", "otherData": "ignored"},
                "Success response",
                "tempContextMessages field should be returned when present"
            ),
            (
                {"data": "D", "result": "B"},
                "Error: Unable to process your request. Unexpected response from Magie API",
                "Error should be returned when tempContextMessages is missing from dict"
            ),
        ]
        
        for api_response, expected_result, description in test_cases:
            with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
                mock_client = AsyncMock()
                mock_client.call_magie_api.return_value = api_response
                mock_get_client.return_value = mock_client
                
                result = await agent.invoke("test query")
                
                if "Error:" in expected_result:
                    assert "Error: Unable to process your request" in result, f"Failed: {description}"
                    assert "Unexpected response from Magie API" in result, f"Failed: {description}"
                else:
                    assert result == expected_result, f"Failed: {description}"
    
    @pytest.mark.asyncio
    async def test_executor_integration_with_agent(self, executor):
        """Test full integration between executor and agent."""
        from a2a.types import MessageSendParams, Message, TextPart, Role
        import uuid
        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.user,
            parts=[TextPart(text="What is the weather like?")],
            metadata={"userId": "00000000-0000-4000-8000-000000000001"}
        )
        request = MessageSendParams(message=message)
        context = RequestContext(request=request)
        event_queue = EventQueue()
        
        # Mock the agent's invoke method
        with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_invoke.return_value = "The weather is sunny and 25°C"
            
            await executor.execute(context, event_queue)
            
            # Verify agent was called correctly with user_id from metadata
            mock_invoke.assert_called_once_with("00000000-0000-4000-8000-000000000001")
            
            # Verify event was enqueued correctly
            mock_enqueue.assert_called_once()
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == "The weather is sunny and 25°C"

    @pytest.mark.asyncio
    async def test_executor_integration_real_agent_success(self, executor):
        """Executor uses real agent.invoke with mocked HTTP client, end-to-end success."""
        from a2a.types import MessageSendParams, Message, TextPart, Role
        import uuid

        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.user,
            parts=[TextPart(text="Qual é o tempo hoje?")],
            metadata={"userId": "00000000-0000-4000-8000-000000000001"}
        )
        request = MessageSendParams(message=message)
        context = RequestContext(request=request)
        event_queue = EventQueue()

        # Patch downstream HTTP client used by agent.invoke
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_client = AsyncMock()
            mock_client.call_magie_api = AsyncMock(return_value={
                "tempContextMessages": "Está ensolarado"
            })
            mock_get_client.return_value = mock_client

            await executor.execute(context, event_queue)

            mock_client.call_magie_api.assert_called_once_with("00000000-0000-4000-8000-000000000001")
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == "Está ensolarado"

    @pytest.mark.asyncio
    async def test_executor_integration_multiline_input_join(self, executor):
        """Multiple text parts should be joined and passed to invoke as a single string."""
        from a2a.types import MessageSendParams, Message, TextPart, Role
        import uuid

        part1 = TextPart(text="linha 1")
        part2 = TextPart(text="linha 2")
        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.user,
            parts=[part1, part2],
            metadata={"userId": "00000000-0000-4000-8000-000000000001"}
        )
        request = MessageSendParams(message=message)
        context = RequestContext(request=request)
        event_queue = EventQueue()

        with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_invoke.return_value = "ok"

            await executor.execute(context, event_queue)

            # The executor should pass the user_id from metadata to the agent
            mock_invoke.assert_called_once_with("00000000-0000-4000-8000-000000000001")
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == "ok"