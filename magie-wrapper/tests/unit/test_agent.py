import pytest
from unittest.mock import AsyncMock, patch, MagicMock
import json

from magie_wrapper.agent import <PERSON><PERSON><PERSON><PERSON>per<PERSON><PERSON>, MagieWrapperAgentExecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.utils import new_agent_text_message


class TestMagieWrapperAgent:
    """Test cases for the MagieWrapperAgent class."""
    
    @pytest.fixture
    def agent(self):
        """Fixture providing a MagieWrapperAgent instance."""
        return MagieWrapperAgent()
    
    @pytest.mark.asyncio
    async def test_invoke_success_with_tempContextMessages_field(self, agent):
        """Test successful invocation with 'tempContextMessages' field in API result."""
        query = "What is the weather like?"
        api_response = {"tempContextMessages": "The weather is sunny today", "status": "success"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "The weather is sunny today"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_error_with_missing_tempContextMessages(self, agent):
        """Test error handling when dict response doesn't have 'tempContextMessages' field."""
        query = "What is 2 + 2?"
        api_response = {"result": "2 + 2 equals 4", "confidence": 0.95}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert "Error: Unable to process your request" in result
            assert "Unexpected response from Magie API" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_error_with_dict_missing_tempContextMessages(self, agent):
        """Test error handling when dict response has different fields but not 'tempContextMessages'."""
        query = "What is the capital of France?"
        api_response = {"answer": "Paris is the capital of France", "source": "geography_db"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert "Error: Unable to process your request" in result
            assert "Unexpected response from Magie API" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_error_with_custom_field_dict(self, agent):
        """Test error handling when dict response has custom fields but not 'tempContextMessages'."""
        query = "Tell me about Python"
        api_response = {"data": {"description": "Python is a programming language"}, "timestamp": "2024-01-01"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            # Should return error when tempContextMessages is not found in dict
            assert "Error: Unable to process your request" in result
            assert "Unexpected response from Magie API" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_success_with_string_response(self, agent):
        """Test successful invocation with string response from API."""
        query = "Hello"
        api_response = "Hello! How can I help you today?"
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "Hello! How can I help you today?"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_success_with_list_response(self, agent):
        """Test successful invocation with list response from API."""
        query = "List programming languages"
        api_response = ["Python", "JavaScript", "Java", "C++"]
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "['Python', 'JavaScript', 'Java', 'C++']"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_api_error_handling(self, agent):
        """Test error handling when API call fails."""
        query = "What is the weather like?"
        api_error = Exception("API connection failed")
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.side_effect = api_error
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert "Error: Unable to process your request" in result
            assert "API connection failed" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_http_error_handling(self, agent):
        """Test error handling when HTTP error occurs."""
        import httpx
        
        query = "What is the weather like?"
        http_error = httpx.HTTPStatusError(
            "500 Internal Server Error",
            request=MagicMock(),
            response=MagicMock(status_code=500, text="Server error")
        )
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.side_effect = http_error
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert "Error: Unable to process your request" in result
            assert "500 Internal Server Error" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_network_error_handling(self, agent):
        """Test error handling when network error occurs."""
        import httpx
        
        query = "What is the weather like?"
        network_error = httpx.ConnectError("Connection failed")
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.side_effect = network_error
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert "Error: Unable to process your request" in result
            assert "Connection failed" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_empty_query(self, agent):
        """Test invocation with empty query."""
        query = ""
        api_response = {"response": "Empty query received", "status": "warning"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert "Error: Unable to process your request" in result
            assert "Unexpected response from Magie API" in result
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_long_query(self, agent):
        """Test invocation with long query."""
        query = "A" * 1000  # Very long query
        api_response = {"tempContextMessages": "Long query processed successfully", "status": "success"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "Long query processed successfully"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_special_characters(self, agent):
        """Test invocation with special characters in query."""
        query = "What is 2 + 2? (with special chars: @#$%^&*)"
        api_response = {"tempContextMessages": "2 + 2 = 4", "status": "success"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "2 + 2 = 4"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_unicode_characters(self, agent):
        """Test invocation with unicode characters in query."""
        query = "What is the weather like in São Paulo? 🌤️"
        api_response = {"tempContextMessages": "The weather in São Paulo is sunny", "status": "success"}
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "The weather in São Paulo is sunny"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_none_response(self, agent):
        """Test invocation when API returns None."""
        query = "What is the weather like?"
        api_response = None
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "None"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_invoke_boolean_response(self, agent):
        """Test invocation when API returns boolean."""
        query = "Is it raining?"
        api_response = True
        
        with patch('magie_wrapper.agent.get_magie_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = api_response
            mock_get_client.return_value = mock_client
            
            result = await agent.invoke(query)
            
            assert result == "True"
            mock_client.call_magie_api.assert_called_once_with(query)
    
    def test_agent_initialization(self, agent):
        """Test that agent can be initialized properly."""
        assert isinstance(agent, MagieWrapperAgent)
        assert hasattr(agent, 'invoke')
        assert callable(agent.invoke)


class TestMagieWrapperAgentExecutor:
    """Test cases for the MagieWrapperAgentExecutor class."""
    
    @pytest.fixture
    def executor(self):
        """Fixture providing a MagieWrapperAgentExecutor instance."""
        return MagieWrapperAgentExecutor()
    
    @pytest.fixture
    def context(self):
        """Fixture providing a RequestContext instance."""
        from a2a.types import MessageSendParams, Message, TextPart, Role
        import uuid
        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.user,
            parts=[TextPart(text="What is the weather like?")],
            metadata={"userId": "test-user-unit"}
        )
        request = MessageSendParams(message=message)
        return RequestContext(request=request)
    
    @pytest.fixture
    def event_queue(self):
        """Fixture providing an EventQueue instance."""
        return EventQueue()
    
    def test_executor_initialization(self, executor):
        """Test that executor can be initialized properly."""
        assert isinstance(executor, MagieWrapperAgentExecutor)
        assert hasattr(executor, 'agent')
        assert isinstance(executor.agent, MagieWrapperAgent)
        assert hasattr(executor, 'execute')
        assert callable(executor.execute)
        assert hasattr(executor, 'cancel')
        assert callable(executor.cancel)
    
    @pytest.mark.asyncio
    async def test_execute_success(self, executor, context, event_queue):
        """Test successful execution of the agent."""
        api_response = {"tempContextMessages": "The weather is sunny today", "status": "success"}
        
        with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_invoke.return_value = "The weather is sunny today"
            
            await executor.execute(context, event_queue)
            
            mock_invoke.assert_called_once_with("test-user-unit")
            mock_enqueue.assert_called_once()
            
            # Check that the correct event was enqueued
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == "The weather is sunny today"
    
    @pytest.mark.asyncio
    async def test_execute_with_error_response(self, executor, context, event_queue):
        """Test execution when agent returns an error response."""
        error_response = "Error: Unable to process your request. API connection failed"
        
        with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_invoke.return_value = error_response
            
            await executor.execute(context, event_queue)
            
            mock_invoke.assert_called_once_with("test-user-unit")
            mock_enqueue.assert_called_once()
            
            # Check that the error event was enqueued
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == error_response
    
    @pytest.mark.asyncio
    async def test_execute_with_empty_response(self, executor, context, event_queue):
        """Test execution with empty response from agent."""
        with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_invoke.return_value = ""
            
            await executor.execute(context, event_queue)
            
            mock_invoke.assert_called_once_with("test-user-unit")
            mock_enqueue.assert_called_once()
            
            # Check that an empty event was enqueued
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == ""
    
    @pytest.mark.asyncio
    async def test_execute_with_long_response(self, executor, context, event_queue):
        """Test execution with long response from agent."""
        long_response = "A" * 10000  # Very long response
        
        with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
             patch.object(event_queue, 'enqueue_event') as mock_enqueue:
            mock_invoke.return_value = long_response
            
            await executor.execute(context, event_queue)
            
            mock_invoke.assert_called_once_with("test-user-unit")
            mock_enqueue.assert_called_once()
            
            # Check that the long event was enqueued
            enqueued_event = mock_enqueue.call_args[0][0]
            assert enqueued_event.parts[0].root.text == long_response
    
    @pytest.mark.asyncio
    async def test_cancel_raises_exception(self, executor, context, event_queue):
        """Test that cancel method raises an exception."""
        with pytest.raises(Exception, match="Cancel not supported"):
            await executor.cancel(context, event_queue)
    
    @pytest.mark.asyncio
    async def test_execute_with_different_user_inputs(self, executor, event_queue):
        """Test execution with different types of user input."""
        test_cases = [
            "What is 2 + 2?",
            "Tell me a joke",
            "How do I cook pasta?",
            "",  # Empty input
            "A" * 1000,  # Long input
            "Special chars: @#$%^&*()",
            "Unicode: São Paulo 🌤️"
        ]
        
        for user_input in test_cases:
            from a2a.types import MessageSendParams, Message, TextPart, Role
            import uuid
            message = Message(
                messageId=str(uuid.uuid4()),
                role=Role.user,
                parts=[TextPart(text=user_input)],
                metadata={"userId": f"test-user-{user_input[:10]}"}
            )
            request = MessageSendParams(message=message)
            context = RequestContext(request=request)
            expected_response = f"Response to: {user_input}"
            
            with patch.object(executor.agent, 'invoke', new_callable=AsyncMock) as mock_invoke, \
                 patch.object(event_queue, 'enqueue_event') as mock_enqueue:
                mock_invoke.return_value = expected_response
                
                await executor.execute(context, event_queue)
                
                mock_invoke.assert_called_once_with(f"test-user-{user_input[:10]}")
                mock_enqueue.assert_called_once()
                
                # Check that the correct event was enqueued
                enqueued_event = mock_enqueue.call_args[0][0]
                assert enqueued_event.parts[0].root.text == expected_response
                
                # Reset mock for next iteration
                mock_enqueue.reset_mock()

 