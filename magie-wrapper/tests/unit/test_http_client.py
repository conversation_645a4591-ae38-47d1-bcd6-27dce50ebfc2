import asyncio
import os
import pytest
import httpx

from unittest.mock import AsyncMock, MagicMock, patch

from magie_wrapper.http_client import (
    MagieAPIClient,
    get_magie_client,
    close_magie_client,
)


class TestMagieAPIClient:
    """Test cases for the MagieAPIClient class."""

    @pytest.fixture
    def api_url(self):
        """Fixture providing a test API URL."""
        return "https://api.magie.test"

    @pytest.fixture
    def client(self, api_url):
        """Fixture providing a MagieAPIClient instance."""
        with patch.dict(os.environ, {"MAGIE_API_URL": api_url}):
            return MagieAPIClient()

    @pytest.fixture
    def mock_response(self):
        """Fixture providing a mock HTTP response."""
        response = MagicMock()
        response.status_code = 200
        response.json.return_value = {"response": "Test response", "status": "success"}
        response.raise_for_status.return_value = None
        return response

    def test_init_with_env_var(self, api_url):
        """Test client initialization with environment variable."""
        with patch.dict(os.environ, {"MAGIE_API_URL": api_url}):
            client = MagieAPIClient()
            assert client.base_url == api_url

    def test_init_with_base_url_parameter(self, api_url):
        """Test client initialization with base_url parameter."""
        client = MagieAPIClient(base_url=api_url)
        assert client.base_url == api_url

    def test_init_without_url_raises_error(self):
        """Test that initialization without URL raises ValueError."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(
                ValueError, match="MAGIE_API_URL environment variable must be set"
            ):
                MagieAPIClient()

    def test_init_removes_trailing_slash(self, api_url):
        """Test that trailing slash is removed from base URL."""
        client = MagieAPIClient(base_url=f"{api_url}/")
        assert client.base_url == api_url

    def test_client_configuration(self, client):
        """Test that the HTTP client is configured with optimal settings."""
        # Test that the client was created successfully
        assert client.client is not None
        assert isinstance(client.client, httpx.AsyncClient)

        # Test headers
        assert client.client.headers["User-Agent"] == "MagieWrapperAgent/1.0"
        assert client.client.headers["Accept"] == "application/json"
        assert client.client.headers["Content-Type"] == "application/json"

        # Test redirect settings
        assert client.client.follow_redirects is True
        assert client.client.max_redirects == 5

        # Test that the client was created with the expected configuration
        # (We can't directly access http2 attribute, but we can verify the client works)
        assert hasattr(client.client, "_transport")

    @pytest.mark.asyncio
    async def test_call_magie_api_success(self, client, mock_response):
        """Test successful API call."""
        chat_uuid = "123e4567-e89b-12d3-a456-************"
        expected_payload = {"chatUuid": chat_uuid}

        with patch.object(client.client, "post", new_callable=AsyncMock) as mock_post:
            mock_post.return_value = mock_response

            result = await client.call_magie_api(chat_uuid)

            # Verify the call was made correctly
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert call_args[0][0] == f"{client.base_url}/magie-wrapper/completion"
            assert call_args[1]["json"] == expected_payload

            # Verify the result
            assert result == {"response": "Test response", "status": "success"}

    @pytest.mark.asyncio
    async def test_call_magie_api_with_additional_params(self, client, mock_response):
        """Test API call with additional parameters."""
        chat_uuid = "123e4567-e89b-12d3-a456-************"
        additional_params = {"temperature": "celsius", "location": "New York"}

        with patch.object(client.client, "post", new_callable=AsyncMock) as mock_post:
            mock_post.return_value = mock_response

            result = await client.call_magie_api(chat_uuid, **additional_params)

            # Verify the call was made with additional params
            call_args = mock_post.call_args
            expected_payload = {"chatUuid": chat_uuid, **additional_params}
            assert call_args[1]["json"] == expected_payload

    @pytest.mark.asyncio
    async def test_call_magie_api_http_error(self, client):
        """Test API call with HTTP error response."""
        chat_uuid = "123e4567-e89b-12d3-a456-************"

        # Mock HTTP error response
        error_response = MagicMock()
        error_response.status_code = 500
        error_response.text = "Internal Server Error"
        error_response.raise_for_status.side_effect = httpx.HTTPStatusError(
            "500 Internal Server Error", request=MagicMock(), response=error_response
        )

        with patch.object(client.client, "post", new_callable=AsyncMock) as mock_post:
            mock_post.return_value = error_response

            with pytest.raises(httpx.HTTPStatusError):
                await client.call_magie_api(chat_uuid)

    @pytest.mark.asyncio
    async def test_call_magie_api_network_error(self, client):
        """Test API call with network error."""
        chat_uuid = "123e4567-e89b-12d3-a456-************"

        with patch.object(client.client, "post", new_callable=AsyncMock) as mock_post:
            mock_post.side_effect = httpx.ConnectError("Connection failed")

            with pytest.raises(httpx.ConnectError):
                await client.call_magie_api(chat_uuid)

    @pytest.mark.asyncio
    async def test_health_check_success(self, client):
        """Test successful health check."""
        health_response = MagicMock()
        health_response.status_code = 200

        with patch.object(client.client, "get", new_callable=AsyncMock) as mock_get:
            mock_get.return_value = health_response

            result = await client.health_check()

            assert result is True
            mock_get.assert_called_once_with(f"{client.base_url}/health")

    @pytest.mark.asyncio
    async def test_health_check_failure(self, client):
        """Test health check failure."""
        with patch.object(client.client, "get", new_callable=AsyncMock) as mock_get:
            mock_get.side_effect = Exception("Connection failed")

            result = await client.health_check()

            assert result is False

    @pytest.mark.asyncio
    async def test_context_manager(self, api_url):
        """Test client as async context manager."""
        with patch.dict(os.environ, {"MAGIE_API_URL": api_url}):
            async with MagieAPIClient() as client:
                assert isinstance(client, MagieAPIClient)
                assert client.base_url == api_url

    @pytest.mark.asyncio
    async def test_close_client(self, client):
        """Test client close method."""
        with patch.object(
            client.client, "aclose", new_callable=AsyncMock
        ) as mock_close:
            await client.close()
            mock_close.assert_called_once()


class TestGlobalClient:
    """Test cases for global client management."""

    @pytest.fixture(autouse=True)
    def reset_global_client(self):
        """Reset global client before and after each test."""
        # Reset before test
        asyncio.run(close_magie_client())
        yield
        # Reset after test
        asyncio.run(close_magie_client())

    @pytest.mark.asyncio
    async def test_get_magie_client_creates_new_instance(self):
        """Test that get_magie_client creates a new instance when none exists."""
        with patch.dict(os.environ, {"MAGIE_API_URL": "https://api.test"}):
            client = await get_magie_client()
            assert isinstance(client, MagieAPIClient)
            assert client.base_url == "https://api.test"

    @pytest.mark.asyncio
    async def test_get_magie_client_reuses_instance(self):
        """Test that get_magie_client reuses existing instance."""
        with patch.dict(os.environ, {"MAGIE_API_URL": "https://api.test"}):
            client1 = await get_magie_client()
            client2 = await get_magie_client()
            assert client1 is client2

    @pytest.mark.asyncio
    async def test_close_magie_client(self):
        """Test that close_magie_client properly closes and resets the global client."""
        with patch.dict(os.environ, {"MAGIE_API_URL": "https://api.test"}):
            # Get a client
            client = await get_magie_client()

            # Mock the close method
            with patch.object(client, "close", new_callable=AsyncMock) as mock_close:
                await close_magie_client()
                mock_close.assert_called_once()

            # Verify a new client is created after closing
            new_client = await get_magie_client()
            assert new_client is not client


class TestAgentIntegration:
    """Test cases for agent integration with HTTP client."""

    @pytest.mark.asyncio
    async def test_agent_invoke_success(self):
        """Test successful agent invocation."""
        from magie_wrapper.agent import MagieWrapperAgent

        agent = MagieWrapperAgent()
        chat_uuid = "123e4567-e89b-12d3-a456-************"
        expected_response = {
            "tempContextMessages": "The weather is sunny",
            "status": "success",
        }

        with patch("magie_wrapper.agent.get_magie_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.return_value = expected_response
            mock_get_client.return_value = mock_client

            result = await agent.invoke(chat_uuid)

            assert result == "The weather is sunny"
            mock_client.call_magie_api.assert_called_once_with(chat_uuid)

    @pytest.mark.asyncio
    async def test_agent_invoke_with_different_response_formats(self):
        """Test agent invocation with different API response formats."""
        from magie_wrapper.agent import MagieWrapperAgent

        agent = MagieWrapperAgent()
        chat_uuid = "123e4567-e89b-12d3-a456-************"

        test_cases = [
            ({"tempContextMessages": "Weather is good"}, "Weather is good"),
            ({"tempContextMessages": "It's raining"}, "It's raining"),
            ({"tempContextMessages": "Some data"}, "Some data"),
            ("Simple string response", "Simple string response"),
        ]

        for api_response, expected_result in test_cases:
            with patch("magie_wrapper.agent.get_magie_client") as mock_get_client:
                mock_client = AsyncMock()
                mock_client.call_magie_api.return_value = api_response
                mock_get_client.return_value = mock_client

                result = await agent.invoke(chat_uuid)

                assert result == expected_result

    @pytest.mark.asyncio
    async def test_agent_invoke_error_handling(self):
        """Test agent error handling during invocation."""
        from magie_wrapper.agent import MagieWrapperAgent

        agent = MagieWrapperAgent()
        chat_uuid = "123e4567-e89b-12d3-a456-************"

        with patch("magie_wrapper.agent.get_magie_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_client.call_magie_api.side_effect = Exception("API Error")
            mock_get_client.return_value = mock_client

            result = await agent.invoke(chat_uuid)

            assert "Error: Unable to process your request" in result
            assert "API Error" in result
