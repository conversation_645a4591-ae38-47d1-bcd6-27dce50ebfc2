.PHONY: web eval test

web:
	uv run adk web src --port 10002

eval:
	uv run adk eval src/math_agent/ src/math_agent/basic.evalset.json

test: test/unit test/integration

test/unit:
	PYTHONPATH=src:. uv run pytest tests/unit/ -v

test/integration:
	PYTHONPATH=src:. uv run pytest tests/integration/ -v

test/mocked:
	PYTHONPATH=src:. uv run pytest tests/integration/test_agent_mocked.py -v

test/e2e:
	@echo "TODO: Add e2e tests"