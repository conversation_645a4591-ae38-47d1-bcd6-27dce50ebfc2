{"family": "math-agent-prod", "taskRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "math-agent-prod", "image": "836929571495.dkr.ecr.us-east-1.amazonaws.com/math-agent:prod", "memoryReservation": 1024, "memory": 1024, "portMappings": [{"containerPort": 10002, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:DD_API_KEY::"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS_JSON", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:GOOGLE_APPLICATION_CREDENTIALS_JSON::"}, {"name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:GOOGLE_API_KEY::"}, {"name": "GOOGLE_GENAI_USE_VERTEXAI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:GOOGLE_GENAI_USE_VERTEXAI::"}, {"name": "GOOGLE_CLOUD_PROJECT", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:GOOGLE_CLOUD_PROJECT::"}, {"name": "GOOGLE_CLOUD_LOCATION", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:GOOGLE_CLOUD_LOCATION::"}, {"name": "MATH_AGENT_URI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/math-agent/secrets-wwvfSE:MATH_AGENT_URI::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/prod/math-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "prod"}, {"name": "DD_SERVICE", "value": "math-agent"}, {"name": "DD_VERSION", "value": "1.0"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS", "value": "/app/secrets/agent-server-key.json"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"math-agent\"}]", "com.datadoghq.tags.env": "prod", "com.datadoghq.tags.service": "math-agent", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "1024"}