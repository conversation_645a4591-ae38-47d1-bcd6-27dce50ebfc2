[project]
name = "math-agent"
version = "0.1.0"
description = "A specialized agent for performing mathematical computations and solving math-related queries in the A2A project."
requires-python = "~=3.13.0"
dependencies = [
    "a2a-sdk~=0.3.3",
    "google-adk~=1.12.0",
    "python-dotenv~=1.1.1",
    "uvicorn~=0.35.0",
    "psycopg2-binary>=2.9.0",
    "sqlalchemy>=2.0.0",
    # internals
    "commons",
] 

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "google-adk[eval]~=1.12.0",
    "pytest-asyncio>=1.1.0",
    "pytest>=8.4.1",
]


[tool.uv.workspace]
members = ["../commons"]

[tool.uv.sources]
commons = { workspace = true }
