# Math Agent Tests

This directory contains comprehensive tests for the math-agent project.

## Test Structure

### Unit Tests (`tests/unit/`)
- **`test_agent.py`**: Comprehensive unit tests for the math agent functionality
  - `TestCalculator`: Tests for the calculator function
    - Basic arithmetic operations
    - Math functions (sqrt, cos, sin, log, exp)
    - Complex expressions
    - Error handling
    - Security (preventing dangerous operations)
    - Math constants (pi, e, tau)
    - Floating point arithmetic
  - `TestAgentCreation`: Tests for agent creation and configuration
    - Agent instantiation
    - Name, description, and model verification
    - Tool configuration
    - Instruction validation
  - `TestMathInstruction`: Tests for the instruction content
  - `TestAgentIntegration`: Mocked integration tests

### Integration Tests (`tests/integration/`)
- **`test_evalset.py`**: Tests for evaluation set functionality
  - File structure validation
  - Agent creation without external dependencies
  - Conditional external API testing (skipped when credentials unavailable)
- **`test_agent_mocked.py`**: Mocked integration tests
  - Agent creation and configuration
  - Calculator tool integration
  - Error handling
  - Eval set file validation
  - Security testing

## Test Coverage

The tests cover:

1. **Calculator Function**:
   - ✅ Basic arithmetic operations
   - ✅ Mathematical functions from math module
   - ✅ Complex expressions
   - ✅ Error handling for invalid expressions
   - ✅ Security against dangerous operations
   - ✅ Math constants
   - ✅ Floating point arithmetic

2. **Agent Creation**:
   - ✅ Proper LlmAgent instantiation
   - ✅ Correct configuration (name, model, description)
   - ✅ Tool assignment
   - ✅ Instruction validation
   - ✅ Root agent availability

3. **Integration**:
   - ✅ Agent creation with mocked dependencies
   - ✅ Calculator tool usage
   - ✅ Error handling scenarios
   - ✅ Eval set file validation
   - ✅ Security testing

## Running Tests

```bash
# Run all tests
make test

# Run only unit tests
make test/unit

# Run only integration tests
make test/integration

# Run mocked integration tests
make test/mocked
```

## Test Configuration

- **pytest.ini**: Configuration for test discovery and execution
- **Makefile**: Convenient commands for running different test suites
- **PYTHONPATH**: Set to include the src directory for proper imports

## External Dependencies

The tests are designed to work without external dependencies:
- Unit tests use mocked components
- Integration tests skip external API calls when credentials are unavailable
- All tests can run in CI/CD environments without Google Cloud setup

## Security Testing

The calculator function is tested against:
- Dangerous built-in functions
- File system access attempts
- Code execution attempts
- Import statements

All security tests verify that dangerous operations are properly blocked.
