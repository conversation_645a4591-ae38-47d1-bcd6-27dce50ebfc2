import logging
import json
import os
import pytest
from google.adk.evaluation.agent_evaluator import AgentEvaluator

logger = logging.getLogger(__name__)

def test_evalset_file_structure():
    """Test that the evaluation set file has the correct structure."""
    evalset_path = "src/math_agent/basic.evalset.json"
    
    assert os.path.exists(evalset_path), f"Eval set file not found: {evalset_path}"
    
    with open(evalset_path, 'r') as f:
        evalset_data = json.load(f)
    
    # Validate required fields
    required_fields = ["eval_set_id", "name", "eval_cases", "criteria"]
    for field in required_fields:
        assert field in evalset_data, f"Missing required field: {field}"
    
    # Validate eval cases
    assert isinstance(evalset_data["eval_cases"], list)
    assert len(evalset_data["eval_cases"]) > 0
    
    # Validate criteria
    required_criteria = ["tool_trajectory_avg_score", "response_match_score"]
    for criterion in required_criteria:
        assert criterion in evalset_data["criteria"], f"Missing required criterion: {criterion}"
        assert isinstance(evalset_data["criteria"][criterion], (int, float))

@pytest.mark.asyncio
async def test_with_single_test_file():
    """Test the agent's basic ability via a session file."""
    # Skip if Google credentials are not available in the environment
    if not os.getenv("GOOGLE_APPLICATION_CREDENTIALS") and not os.getenv("GOOGLE_API_KEY"):
        pytest.skip("Google Cloud credentials not available - skipping external API test")

    try:
        criteria = {
            "tool_trajectory_avg_score": 0.5,
            "response_match_score": 0.5,
        }
        eval_set = AgentEvaluator._load_eval_set_from_file(
            "src/math_agent/basic.evalset.json", criteria, {}
        )
        await AgentEvaluator.evaluate_eval_set(
            agent_module="math_agent",
            eval_set=eval_set,
            criteria=criteria,
            num_runs=1,
            agent_name="math_agent",
        )
    except Exception as e:
        # Skip on auth/credential related failures
        if "credentials" in str(e).lower() or "authentication" in str(e).lower():
            pytest.skip(f"Authentication failed - skipping external API test: {e}")
        else:
            raise
 
