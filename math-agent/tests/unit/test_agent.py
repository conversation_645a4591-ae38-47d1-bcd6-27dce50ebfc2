import pytest
from unittest.mock import Mock, patch
import math

from math_agent.agent import calculator, create_agent, _MATH_INSTRUCTION


class TestCalculator:
    """Test cases for the calculator function."""
    
    def test_basic_arithmetic(self):
        """Test basic arithmetic operations."""
        test_cases = [
            ("2 + 2", 4),
            ("10 - 5", 5),
            ("3 * 4", 12),
            ("15 / 3", 5.0),
            ("2 ** 3", 8),
            ("10 % 3", 1),
        ]
        
        for expression, expected in test_cases:
            result = calculator(expression)
            assert result["status"] == "success"
            assert result["result"] == expected
    
    def test_math_functions(self):
        """Test mathematical functions from math module."""
        test_cases = [
            ("sqrt(16)", 4.0),
            ("cos(0)", 1.0),
            ("sin(0)", 0.0),
            ("log(1)", 0.0),
            ("exp(0)", 1.0),
        ]
        
        for expression, expected in test_cases:
            result = calculator(expression)
            assert result["status"] == "success"
            assert result["result"] == expected
    
    def test_complex_expressions(self):
        """Test complex mathematical expressions."""
        test_cases = [
            ("(2 + 3) * 4", 20),
            ("sqrt(25) + 5", 10.0),
            ("cos(0) * 2", 2.0),
        ]
        
        for expression, expected in test_cases:
            result = calculator(expression)
            assert result["status"] == "success"
            assert result["result"] == expected
    
    def test_error_handling(self):
        """Test error handling for invalid expressions."""
        invalid_expressions = [
            "invalid_expression",
            "2 / 0",  # Division by zero
            "sqrt(-1)",  # Complex number
            "undefined_function(5)",
            "print('hello')",  # Built-in function
        ]
        
        for expression in invalid_expressions:
            result = calculator(expression)
            assert result["status"] == "error"
            assert "error_message" in result
            assert isinstance(result["error_message"], str)
    
    def test_safe_math_functions_only(self):
        """Test that only safe math functions are available."""
        # Test that dangerous built-ins are not available
        dangerous_expressions = [
            "__import__('os')",
            "open('file.txt')",
            "eval('2+2')",
            "exec('print(1)')",
        ]
        
        for expression in dangerous_expressions:
            result = calculator(expression)
            assert result["status"] == "error"
    
    def test_math_constants(self):
        """Test mathematical constants."""
        test_cases = [
            ("pi", math.pi),
            ("e", math.e),
            ("tau", math.tau),
        ]
        
        for expression, expected in test_cases:
            result = calculator(expression)
            assert result["status"] == "success"
            assert result["result"] == expected
    
    def test_floating_point_arithmetic(self):
        """Test floating point arithmetic."""
        test_cases = [
            ("3.14 + 2.86", 6.0),
            ("10.5 / 2", 5.25),
            ("2.5 * 3", 7.5),
        ]
        
        for expression, expected in test_cases:
            result = calculator(expression)
            assert result["status"] == "success"
            assert result["result"] == expected


class TestAgentCreation:
    """Test cases for agent creation and configuration."""
    
    def test_create_agent_returns_llm_agent(self):
        """Test that create_agent returns an LlmAgent instance."""
        from google.adk.agents import LlmAgent
        
        agent = create_agent()
        assert isinstance(agent, LlmAgent)
    
    def test_agent_name(self):
        """Test that the agent has the correct name."""
        agent = create_agent()
        assert agent.name == "math_agent"
    
    def test_agent_description(self):
        """Test that the agent has the correct description."""
        agent = create_agent()
        expected_description = "Agent to answer mathematical questions and perform calculations."
        assert agent.description == expected_description
    
    def test_agent_instruction(self):
        """Test that the agent has the correct instruction."""
        agent = create_agent()
        assert agent.instruction == _MATH_INSTRUCTION
    
    def test_agent_has_calculator_tool(self):
        """Test that the agent has the calculator tool."""
        agent = create_agent()
        assert len(agent.tools) == 1
        assert agent.tools[0] == calculator
    
    def test_agent_model(self):
        """Test that the agent uses the correct model."""
        agent = create_agent()
        assert agent.model == "gemini-2.5-flash"
    
    def test_root_agent_exists(self):
        """Test that root_agent is available for ADK CLI."""
        from math_agent.agent import root_agent
        assert root_agent is not None
        assert root_agent.name == "math_agent"


class TestMathInstruction:
    """Test cases for the math instruction constant."""
    
    def test_instruction_content(self):
        """Test that the instruction contains expected content."""
        assert "math agent" in _MATH_INSTRUCTION.lower()
        assert "solve mathematical problems" in _MATH_INSTRUCTION
        assert "calculator tool" in _MATH_INSTRUCTION
        assert "step by step" in _MATH_INSTRUCTION
    
    def test_instruction_format(self):
        """Test that the instruction has proper formatting."""
        assert "<System>" in _MATH_INSTRUCTION
        assert "</System>" in _MATH_INSTRUCTION
        assert len(_MATH_INSTRUCTION) > 100  # Should be substantial


class TestAgentIntegration:
    """Integration tests for the agent (mocked)."""
    
    @pytest.mark.asyncio
    async def test_agent_creation_with_mock_llm(self):
        """Test agent creation with mocked LLM dependencies."""
        with patch('math_agent.agent.LlmAgent') as mock_llm_agent:
            mock_agent_instance = Mock()
            mock_llm_agent.return_value = mock_agent_instance
            
            agent = create_agent()
            
            mock_llm_agent.assert_called_once_with(
                name="math_agent",
                model="gemini-2.5-flash",
                description="Agent to answer mathematical questions and perform calculations.",
                instruction=_MATH_INSTRUCTION,
                tools=[calculator],
            )
            assert agent == mock_agent_instance
    
    def test_calculator_function_signature(self):
        """Test that calculator function has the correct signature."""
        import inspect
        
        sig = inspect.signature(calculator)
        params = list(sig.parameters.keys())
        
        assert len(params) == 1
        assert params[0] == "expression"
        assert sig.parameters["expression"].annotation is str
    
    def test_calculator_return_type(self):
        """Test that calculator returns the expected dictionary structure."""
        result = calculator("2 + 2")
        
        assert isinstance(result, dict)
        assert "status" in result
        assert "result" in result or "error_message" in result
        
        if result["status"] == "success":
            assert "result" in result
        elif result["status"] == "error":
            assert "error_message" in result
