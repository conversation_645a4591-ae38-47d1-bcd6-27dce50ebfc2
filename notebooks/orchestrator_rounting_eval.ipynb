{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup DB\n", "- Database Connection\n", "- Dataset retrieval\n", "- Pandas format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine\n", "import pandas as pd\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv(\"../.env\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["engine = create_engine(os.getenv('DB_URL'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["magie_ds_name = 'BANKING_FINE_TUNING_20_A_31_JANEIRO_2025'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# rows_in_chunk = 1_000\n", "# chunks = pd.read_sql_query(q, conn, chunksize=rows_in_chunk)\n", "# df = tqdm(chunks, total=total_rows/rows_in_chunk)\n", "# df = pd.concat(df)\n", "\n", "sql_query = f\"\"\"\n", "SELECT cma.expected_function, ccr.*\n", "FROM data_processor.chat_message_analysis cma\n", "         JOIN llm.chat_completion_requests ccr ON cma.llm_chat_completion_request_uuid = ccr.uuid\n", "WHERE cma.dataset_name = '{magie_ds_name}'\n", "  AND cma.analysis_completed  = TRUE\n", "  AND cma.analysis_completed  = TRUE\n", "  AND cma.invoked_function_is_correct = TRUE\n", "  AND cma.invoked_function_arguments_is_correct = TRUE\n", "  AND cma.error = FALSE\n", "  AND cma.expected_function <> 'null';\n", "\"\"\"\n", "df = pd.read_sql_query(sql_query, engine, dtype=str).dropna()\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Filter DF\n", "- Get 10 messages per tool\n", "- Filter bad formated abstract_requests\n", "- Filter bad tools to evaluate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def validate_abstract_request(abstract_request):\n", "    \"\"\"\n", "    Valida se o abstract_request contém mensagens válidas.\n", "    \"\"\"\n", "    try:\n", "        # Se abstract_request é uma string, converte para dict/list\n", "        if isinstance(abstract_request, str):\n", "            json_request = json.loads(abstract_request)\n", "        else:\n", "            json_request = abstract_request\n", "\n", "        messages = json_request[\"messages\"]\n", "\n", "        # Verifica se todas as mensagens de user têm 'role' e 'content'\n", "        # Se for assistant, pode não ter content, mas deve ter toolCalls\n", "        for message in messages:\n", "            if message[\"role\"] == \"user\":\n", "                if \"content\" not in message:\n", "                    return False\n", "            elif message[\"role\"] == \"assistant\":\n", "                if \"content\" not in message and \"toolCalls\" not in message:\n", "                    return False\n", "        return True\n", "    except:\n", "        return False\n", "    \n", "def get_valid_messages_per_tool(df, tool, messages_per_tool):\n", "    \"\"\"\n", "    Busca mensagens válidas para uma ferramenta específica.\n", "    Continua buscando até encontrar o número desejado de mensagens válidas\n", "    ou até esgotar todas as mensagens disponíveis.\n", "    \"\"\"\n", "    # Filtra mensagens da ferramenta\n", "    tool_messages = df[df[\"expected_function\"] == tool].copy()\n", "    \n", "    valid_messages = []\n", "    index = 0\n", "    \n", "    while len(valid_messages) < messages_per_tool and index < len(tool_messages):\n", "        current_batch = tool_messages.iloc[index:index + (messages_per_tool - len(valid_messages))]\n", "        \n", "        for _, row in current_batch.iterrows():\n", "            if validate_abstract_request(row[\"abstract_request\"]):\n", "                valid_messages.append(row)\n", "            \n", "            # Se já temos mensagens suficientes, para\n", "            if len(valid_messages) >= messages_per_tool:\n", "                break\n", "        \n", "        index += len(current_batch)\n", "    \n", "    return pd.DataFrame(valid_messages) if valid_messages else pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MESSAGES_PER_TOOL = 10\n", "expected_tools = df[\"expected_function\"].unique()\n", "\n", "filtered_df = pd.DataFrame()\n", "\n", "# Iterate over all expected tools\n", "for tool in expected_tools:\n", "    # Busca mensagens válidas para esta ferramenta\n", "    valid_messages = get_valid_messages_per_tool(df, tool, MESSAGES_PER_TOOL)\n", "    \n", "    if not valid_messages.empty:\n", "        filtered_df = pd.concat([filtered_df, valid_messages], ignore_index=True)\n", "    else:\n", "        print(f\"  Nenhuma mensagem válida encontrada para {tool}\")\n", "\n", "# Atualiza o DataFrame original\n", "df = filtered_df\n", "\n", "df[\"expected_function\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Excluir funções que não são relevantes para o dataset\n", "tools_to_exclude = [\"free_pix\"]\n", "\n", "# Filtrando o DataFrame para remover essas funções\n", "df = df[~df[\"expected_function\"].isin(tools_to_exclude)]\n", "\n", "df[\"expected_function\"].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Clean messages\n", "- Remove initial and final system messages\n", "- Remove assitant tool calls\n", "- Remove tool responses"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_conversation_messages(messages):\n", "    \"\"\"\n", "    Cleans conversation JSON by removing unwanted messages\n", "    \n", "    Args:\n", "        messages (list): List of message dictionaries\n", "        \n", "    Returns:\n", "        list: Cleaned list of messages\n", "    \"\"\"\n", "    if not messages or not isinstance(messages, list):\n", "        return []\n", "    \n", "    # Step 1: Find the first user message index\n", "    first_user_index = None\n", "    for i, msg in enumerate(messages):\n", "        if msg.get('role') == 'user':\n", "            first_user_index = i\n", "            break\n", "    \n", "    # Step 2: Find the last user message index\n", "    last_user_index = None\n", "    for i in range(len(messages) - 1, -1, -1):\n", "        if messages[i].get('role') == 'user':\n", "            last_user_index = i\n", "            break\n", "    \n", "    # If no user messages found, return empty list\n", "    if first_user_index is None or last_user_index is None:\n", "        return []\n", "    \n", "    # Step 3: Get messages between first and last user messages (inclusive)\n", "    # This automatically removes system messages before first and after last user\n", "    messages_in_range = messages[first_user_index:last_user_index + 1]\n", "    \n", "    # Step 4: Filter out unwanted message types (but keep system messages in the middle)\n", "    filtered_messages = []\n", "    \n", "    for msg in messages_in_range:\n", "        role = msg.get('role')\n", "        \n", "        # Remove assistant messages that have toolCalls array filled\n", "        if role == 'assistant':\n", "            tool_calls = msg.get('toolCalls')\n", "            if tool_calls and isinstance(tool_calls, list) and len(tool_calls) > 0:\n", "                continue\n", "        \n", "        # Remove tool response messages\n", "        if role == 'tool':\n", "            continue\n", "            \n", "        # Keep everything else (user, assistant without toolCalls, and system messages in the middle)\n", "        filtered_messages.append(msg)\n", "    \n", "    return filtered_messages\n", "\n", "\n", "def clean_conversation_messages_explicit(messages):\n", "    \"\"\"\n", "    Alternative implementation with more explicit steps\n", "    \n", "    Args:\n", "        messages (list): List of message dictionaries\n", "        \n", "    Returns:\n", "        list: Cleaned list of messages\n", "    \"\"\"\n", "    if not messages or not isinstance(messages, list):\n", "        return []\n", "    \n", "    # Step 1: Find first and last user message indices\n", "    first_user_index = None\n", "    last_user_index = None\n", "    \n", "    for i, msg in enumerate(messages):\n", "        if msg.get('role') == 'user':\n", "            if first_user_index is None:\n", "                first_user_index = i\n", "            last_user_index = i  # Keep updating to get the last one\n", "    \n", "    # If no user messages found, return empty list\n", "    if first_user_index is None or last_user_index is None:\n", "        return []\n", "    \n", "    # Step 2: Remove system messages before first user and after last user\n", "    # by slicing the array to only include messages between first and last user (inclusive)\n", "    result = messages[first_user_index:last_user_index + 1]\n", "    \n", "    # Step 3: Remove assistant messages which have toolCalls array filled\n", "    result = [\n", "        msg for msg in result \n", "        if not (\n", "            msg.get('role') == 'assistant' and \n", "            msg.get('toolCalls') and \n", "            isinstance(msg.get('toolCalls'), list) and \n", "            len(msg.get('toolCalls')) > 0\n", "        )\n", "    ]\n", "    \n", "    # Step 4: Remove tool response messages\n", "    result = [msg for msg in result if msg.get('role') != 'tool']\n", "    \n", "    # Note: We do NOT remove system messages here because we want to keep\n", "    # system messages that are in the middle of the conversation\n", "    \n", "    return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup Dataset Examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Initialize the examples array\n", "examples = []\n", "\n", "for x in range(len(df)):\n", "    # Get the abstract request and parse it as JSON\n", "    json_data = json.loads(df.iloc[x].abstract_request)\n", "    \n", "    # Clean the conversation messages\n", "    cleaned_messages = clean_conversation_messages_explicit(json_data['messages'])\n", "    \n", "    # Create the example entry\n", "    example = {\n", "        \"inputs\": {\n", "            \"messages\": cleaned_messages\n", "        },\n", "        \"outputs\": {\n", "            \"answer\": \"\"\n", "        },\n", "        \"metadata\": {\n", "            \"expected_agent\": \"<PERSON>gie <PERSON>per\"\n", "        }\n", "    }\n", "    \n", "    # Add to examples array\n", "    examples.append(example)\n", "\n", "examples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Lanfuse Eval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langfuse import get_client\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "langfuse = get_client()\n", " \n", "# Verify connection\n", "if langfuse.auth_check():\n", "    print(\"Langfuse client is authenticated and ready!\")\n", "else:\n", "    print(\"Authentication failed. Please check your credentials and host.\")\n", "\n", "# print(os.getenv(\"GOOGLE_APPLICATION_CREDENTIALS\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset\n", "- Create Dataset\n", "- Populate Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = langfuse.create_dataset(\n", "    name=\"<PERSON><PERSON>\"\n", ")\n", "\n", "dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "for example in examples:\n", "  time.sleep(0.5)\n", "  langfuse.create_dataset_item(\n", "    dataset_name=dataset.name,\n", "    input=example[\"inputs\"][\"messages\"],\n", "    expected_output=example[\"outputs\"],\n", "    metadata=example[\"metadata\"]\n", "  )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON>l\n", "- Define evaluators\n", "- <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def expected_agent_was_called(response: dict, expected_agent):\n", "    \"\"\"\n", "    Evaluate if the expected agent was called.\n", "    Returns 1 if the expected agent was found in tool calls, 0 otherwise.\n", "    \"\"\"\n", "\n", "    tool_calls = response.get(\"tool_calls\")\n", "\n", "    for tool_call in tool_calls:\n", "        if tool_call.get(\"name\") == \"send_message\" and tool_call.get(\"args\").get(\"agent_name\") == expected_agent:\n", "            return 1\n", "\n", "    return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!uv pip install -e ../orchestrator-agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.getcwd()\n", "\n", "os.environ[\"GOOGLE_APPLICATION_CREDENTIALS\"] = \"/Users/<USER>/projects/magie-agents/secrets/agent-server-key.json\"\n", "os.environ[\"GOOGLE_CLOUD_PROJECT\"] = \"magie-ai-staging\"\n", "os.environ[\"GOOGLE_CLOUD_LOCATION\"] = \"us-central1\"\n", "os.environ[\"GOOGLE_GENAI_USE_VERTEXAI\"] = \"TRUE\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from orchestrator_agent.orchestrator_eval import execute_orchestrator_agent\n", "\n", "async def run_orchestrator_agent(input):\n", "  return await execute_orchestrator_agent(input)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# {'final_response': 'Entendido. Enviei sua solicitação para o Magie Wrapper para realizar o Pix de 1 real para o Gabriel.',\n", "#  'tool_calls': [{'id': 'adk-64c4d777-5f75-42b5-9ec7-58c8b9320406',\n", "#    'name': 'send_message',\n", "#    'args': {'agent_name': '<PERSON><PERSON>',\n", "#     'task': '<PERSON><PERSON>r Pix de 1 real para Gabriel'}}]}\n", "import time\n", "\n", "async def run_orchestrator_agent_eval(experiment_name: str):\n", "    dataset = langfuse.get_dataset(\"Magie Eval\")\n", "\n", "    for item in dataset.items:\n", "\n", "        session_id = f\"experiment_{experiment_name}_{int(time.time())}\"\n", "        \n", "        with item.run(\n", "            run_name = experiment_name,\n", "            run_description=\"My first run\") as root_span:\n", "            response = await run_orchestrator_agent(item.input)\n", "\n", "            root_span.update_trace(\n", "                input=item.input,\n", "                output=response,\n", "                session_id=session_id\n", "            )\n", "\n", "            agent_score = expected_agent_was_called(response, item.metadata.get(\"expected_agent\"))\n", "\n", "            root_span.score_trace(name=\"expected_agent\", value=agent_score)\n", "\n", "await run_orchestrator_agent_eval(\"orchestrator_rounting_eval\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}