FROM python:3.13-slim

# Copy all dependecies for the agent
COPY commons ./commons

# Set the work directory
WORKDIR /app

# Copy all dependency definition files and source code first
COPY orchestrator-agent .

# Copy the entrypoint script
COPY orchestrator-agent/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Install the 'uv' package manager
RUN pip install uv

# Install exact dependencies as specified in uv.lock
# fail if there's a mismatch with pyproject.toml
RUN uv sync --frozen

# Expose the port the application runs on
EXPOSE 10000

# Define the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Command to run the application using ADK web command
CMD ["uv", "run", "adk", "web", "src", "--host", "0.0.0.0", "--port", "10000"]
