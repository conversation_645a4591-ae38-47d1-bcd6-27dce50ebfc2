{"family": "orchestrator-agent", "taskRoleArn": "arn:aws:iam::992382535149:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::992382535149:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "orchestrator-agent", "image": "992382535149.dkr.ecr.us-east-1.amazonaws.com/orchestrator-agent:staging", "memoryReservation": 1024, "memory": 1024, "portMappings": [{"containerPort": 10000, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/shared/secrets-7NSTZq:DD_API_KEY::"}, {"name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:GOOGLE_API_KEY::"}, {"name": "GOOGLE_GENAI_USE_VERTEXAI", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:GOOGLE_GENAI_USE_VERTEXAI::"}, {"name": "GOOGLE_CLOUD_PROJECT", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:GOOGLE_CLOUD_PROJECT::"}, {"name": "GOOGLE_CLOUD_LOCATION", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:GOOGLE_CLOUD_LOCATION::"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS_JSON", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:GOOGLE_APPLICATION_CREDENTIALS_JSON::"}, {"name": "MATH_AGENT_URI", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:MATH_AGENT_URI::"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:DATABASE_URL::"}, {"name": "MEMORY_SERVICE_TYPE", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:MEMORY_SERVICE_TYPE::"}, {"name": "SESSION_SERVICE_TYPE", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:SESSION_SERVICE_TYPE::"}, {"name": "LANGFUSE_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:LANGFUSE_PUBLIC_KEY::"}, {"name": "LANGFUSE_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:LANGFUSE_SECRET_KEY::"}, {"name": "LANGFUSE_HOST", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/orchestrator-agent/secrets-4AXCyu:LANGFUSE_HOST::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/staging/orchestrator-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "new-staging"}, {"name": "DD_SERVICE", "value": "orchestrator-agent"}, {"name": "DD_VERSION", "value": "1.0"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS", "value": "/app/secrets/agent-server-key.json"}], "entryPoint": ["sh", "-c"], "command": ["mkdir -p /app/secrets && printf '%s' \"$GOOGLE_APPLICATION_CREDENTIALS_JSON\" > /app/secrets/agent-server-key.json"], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"orchestrator-agent\"}]", "com.datadoghq.tags.env": "staging", "com.datadoghq.tags.service": "orchestrator-agent", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "1024"}