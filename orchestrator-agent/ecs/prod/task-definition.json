{"family": "orchestrator-agent-prod", "taskRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "orchestrator-agent-prod", "image": "836929571495.dkr.ecr.us-east-1.amazonaws.com/orchestrator-agent:prod", "memoryReservation": 1024, "memory": 1024, "portMappings": [{"containerPort": 10000, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/shared/secrets-HLSQnb:DD_API_KEY::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/prod/orchestrator-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "prod"}, {"name": "DD_SERVICE", "value": "orchestrator-agent"}, {"name": "DD_VERSION", "value": "1.0"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"java\", \"service\": \"orchestrator-agent\"}]", "com.datadoghq.tags.env": "prod", "com.datadoghq.tags.service": "orchestrator-agent", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "1024"}