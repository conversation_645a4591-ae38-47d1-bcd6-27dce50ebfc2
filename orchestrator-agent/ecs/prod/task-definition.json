{"family": "orchestrator-agent-prod", "taskRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "orchestrator-agent-prod", "image": "836929571495.dkr.ecr.us-east-1.amazonaws.com/orchestrator-agent:prod", "memoryReservation": 2048, "memory": 2048, "portMappings": [{"containerPort": 10000, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:DD_API_KEY::"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS_JSON", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:GOOGLE_APPLICATION_CREDENTIALS_JSON::"}, {"name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:GOOGLE_API_KEY::"}, {"name": "GOOGLE_GENAI_USE_VERTEXAI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:GOOGLE_GENAI_USE_VERTEXAI::"}, {"name": "GOOGLE_CLOUD_PROJECT", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:GOOGLE_CLOUD_PROJECT::"}, {"name": "GOOGLE_CLOUD_LOCATION", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:GOOGLE_CLOUD_LOCATION::"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:DATABASE_URL::"}, {"name": "MEMORY_SERVICE_TYPE", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:MEMORY_SERVICE_TYPE::"}, {"name": "SESSION_SERVICE_TYPE", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:SESSION_SERVICE_TYPE::"}, {"name": "LANGFUSE_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:LANGFUSE_PUBLIC_KEY::"}, {"name": "LANGFUSE_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:LANGFUSE_SECRET_KEY::"}, {"name": "LANGFUSE_HOST", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:LANGFUSE_HOST::"}, {"name": "MATH_AGENT_URI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:MATH_AGENT_URI::"}, {"name": "MAGIE_WRAPPER_URI", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/orchestrator-agent/secrets-vj11WY:MAGIE_WRAPPER_URI::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/prod/orchestrator-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "prod"}, {"name": "DD_SERVICE", "value": "orchestrator-agent"}, {"name": "DD_VERSION", "value": "1.0"}, {"name": "GOOGLE_APPLICATION_CREDENTIALS", "value": "/app/secrets/agent-server-key.json"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"orchestrator-agent\"}]", "com.datadoghq.tags.env": "prod", "com.datadoghq.tags.service": "orchestrator-agent", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "2048"}