#!/bin/sh

set -e

# Cria diretório de segredos e grava o conteúdo da variável no arquivo
mkdir -p /app/secrets

if [ -n "$GOOGLE_APPLICATION_CREDENTIALS_JSON" ]; then
  printf '%s' "$GOOGLE_APPLICATION_CREDENTIALS_JSON" > /app/secrets/agent-server-key.json
  echo "[entrypoint] Secret written to /app/secrets/agent-server-key.json"
else
  echo "[entrypoint] WARNING: GOOGLE_APPLICATION_CREDENTIALS_JSON is empty or undefined"
fi

# Executa o comando padrão passado no CMD
exec "$@"
