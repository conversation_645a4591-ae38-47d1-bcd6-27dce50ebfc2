[project]
name = "orchestrator-agent"
version = "0.1.0"
description = "A multi-agent orchestration system that coordinates user requests and delegates tasks to specialized agents."
readme = "README.md"
requires-python = "~=3.13"
dependencies = [
    # Shared ADK & A2A Dependencies
    "a2a-sdk>=0.3.3",
    "google-adk~=1.12.0",
    "nest-asyncio>=1.6.0",
    "python-dotenv",
    "click",
    "uvicorn",
    "httpx",
    # Database dependencies for PostgreSQL session service
    "psycopg2-binary>=2.9.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "langfuse>=3.2.1",
    # Internal dependencies
    "commons",
]

[tool.uv.workspace]
members = ["../commons"]

[tool.uv.sources]
commons = { workspace = true }

[tool.uv]
dev-dependencies = [
    "pytest>=8.4.1",
    "pytest-asyncio>=0.24.0",
]