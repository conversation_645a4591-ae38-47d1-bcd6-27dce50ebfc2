[project]
name = "orchestrator-agent"
version = "0.1.0"
description = "A multi-agent orchestration system that coordinates user requests and delegates tasks to specialized agents."
readme = "README.md"
requires-python = "~=3.13"
dependencies = [
    # Shared ADK & A2A Dependencies
    "google-adk>=1.2.1",
    "a2a-sdk>=0.2.5",
    "nest-asyncio>=1.6.0",
    "python-dotenv",
    "click",
    "uvicorn",
    "google-generativeai",
    "httpx",
    # Database dependencies for PostgreSQL session service
    "psycopg2-binary>=2.9.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "langfuse>=3.2.1",
    # <PERSON><PERSON><PERSON>'s agent dependencies (future)
    # "langgraph"
]

[tool.uv.workspace]
members = ["../commons"]
