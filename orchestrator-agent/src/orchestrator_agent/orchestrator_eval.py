from datetime import datetime
from typing import List
import uuid
from google.adk import Runner
from google.genai import types
from google.adk.events import Event
from google.adk.sessions import InMemorySessionService
from .agent import root_agent
from dotenv import load_dotenv

load_dotenv("../.env")


def get_author(message: str):
  if message.get("role") == "user":
    return "user"
  elif message.get("role") == "assistant":
    return "model"
  else:
    return "chatbot"

async def execute_orchestrator_agent(messages: List[dict]):

  session_id = datetime.now().strftime("%Y%m%d%H%M%S")

  session_service = InMemorySessionService()

  session = await session_service.create_session(
    app_name="orchestrator_agent",
    user_id="magie",
    session_id=session_id
  )

  for message in messages[:-1]:

    author = get_author(message)
    await session_service.append_event(
      session=session,
      event=Event(
        invocation_id=str(uuid.uuid4()),
        author=author,
        timestamp=datetime.now().timestamp(),
        content=types.Content(
          role="model" if message.get("role") != "user" else "user",
          parts=[
            types.Part(
              text=message.get("content")
            )
          ]
        )
      )
    )

  runner = Runner(agent=root_agent, app_name="orchestrator_agent", session_service=session_service)
  # Consume async generator and return structured result with final response and tool calls
  final_text = ""
  tool_calls = []
  async for event in runner.run_async(
    session_id=session_id,
    user_id="magie",
    new_message=types.Content(
      role="user",
      parts=[
        types.Part(
          text=messages[-1].get("content")
        )
      ]
    )
  ):
    # Collect tool calls along the way
    for fc in (event.get_function_calls() or []):
      tool_calls.append({
        "id": getattr(fc, "id", None),
        "name": getattr(fc, "name", None),
        "args": getattr(fc, "args", None),
      })
    if event.is_final_response():
      if event.content and event.content.parts:
        final_text = "\n".join([p.text for p in event.content.parts if getattr(p, "text", None)])
  return {"final_response": final_text, "tool_calls": tool_calls}