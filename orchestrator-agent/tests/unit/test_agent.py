import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from google.adk.tools.tool_context import ToolContext
from a2a.types import <PERSON><PERSON><PERSON>, SendMessageSuccessResponse, Task

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from orchestrator_agent.agent import HostAgent
from orchestrator_agent.remote_agent_connection import RemoteAgentConnections


class TestHostAgentSendMessage:
    """Test cases for the HostAgent send_message method."""

    @pytest.fixture
    def mock_agent_card(self):
        """Fixture providing a mock agent card."""
        card = MagicMock(spec=AgentCard)
        card.name = "test_agent"
        card.description = "Test agent for unit testing"
        return card

    @pytest.fixture
    def mock_remote_connection(self, mock_agent_card):
        """Fixture providing a mock remote agent connection."""
        connection = MagicMock(spec=RemoteAgentConnections)
        connection.agent_card = mock_agent_card
        connection.agent_url = "http://localhost:8080"
        connection.send_message = AsyncMock()
        return connection

    @pytest.fixture
    def host_agent(self, mock_remote_connection):
        """Fixture providing a HostAgent instance with mocked dependencies."""
        agent = HostAgent()
        agent.remote_agent_connections = {"test_agent": mock_remote_connection}
        agent.cards = {"test_agent": mock_remote_connection.agent_card}
        return agent

    @pytest.fixture
    def mock_tool_context(self):
        """Fixture providing a mock tool context with user_id."""
        context = MagicMock(spec=ToolContext)
        context.state = {"task_id": "test-task-123", "context_id": "test-context-456"}
        
        # Mock the invocation context with user_id
        invocation_context = MagicMock()
        invocation_context.user_id = "test-user-789"
        context._invocation_context = invocation_context
        
        return context

    @pytest.fixture
    def mock_send_response(self):
        """Fixture providing a mock successful send response."""
        response = MagicMock()
        response.root = MagicMock(spec=SendMessageSuccessResponse)
        response.root.result = MagicMock(spec=Task)
        response.root.model_dump_json.return_value = '{"result": {"artifacts": []}}'
        return response

    @pytest.mark.asyncio
    async def test_send_message_user_id_in_metadata(
        self, host_agent, mock_tool_context, mock_send_response
    ):
        """Test that user_id is correctly passed in payload.message.metadata."""
        # Arrange
        agent_name = "test_agent"
        task = "Test task message"
        expected_user_id = "test-user-789"
        
        # Mock the remote connection's send_message method
        mock_connection = host_agent.remote_agent_connections[agent_name]
        mock_connection.send_message.return_value = mock_send_response

        # Act
        await host_agent.send_message(agent_name, task, mock_tool_context)

        # Assert
        # Verify send_message was called
        mock_connection.send_message.assert_called_once()
        
        # Get the call arguments
        call_args = mock_connection.send_message.call_args
        message_request = call_args[0][0]  # First argument is the SendMessageRequest
        
        # Verify the payload structure
        payload = message_request.params.model_dump()
        message = payload["message"]
        
        # Verify user_id is in metadata
        assert "metadata" in message
        assert "userId" in message["metadata"]
        assert message["metadata"]["userId"] == expected_user_id
        
        # Verify other payload fields
        assert message["role"] == "user"
        assert message["parts"] == [{"kind": "text", "text": task, "metadata": None}]
        assert "messageId" in message
        assert "taskId" in message
        assert "contextId" in message

    @pytest.mark.asyncio
    async def test_send_message_agent_not_found(self, host_agent, mock_tool_context):
        """Test that send_message returns a tool-friendly error for non-existent agent."""
        # Arrange
        agent_name = "non_existent_agent"
        task = "Test task message"

        # Act
        resp = await host_agent.send_message(agent_name, task, mock_tool_context)
        # Assert
        assert isinstance(resp, list)
        assert resp and resp[0].get("type") == "text"
        assert "not found" in resp[0].get("text")

    @pytest.mark.asyncio
    async def test_send_message_client_not_available(self, host_agent, mock_tool_context):
        """Test that send_message returns a tool-friendly error when client is unavailable."""
        # Arrange
        agent_name = "test_agent"
        task = "Test task message"
        
        # Set client to None
        host_agent.remote_agent_connections[agent_name] = None

        # Act
        resp = await host_agent.send_message(agent_name, task, mock_tool_context)
        # Assert
        assert isinstance(resp, list)
        assert resp and resp[0].get("type") == "text"
        assert "unavailable" in resp[0].get("text")
