FROM python:3.13-slim

# Copy all dependecies for the agent
COPY commons ./commons

# Set the work directory
WORKDIR /app

# Copy all dependency definition files and source code first
COPY web-search-agent .

# Install the 'uv' package manager
RUN pip install uv

# Install exact dependencies as specified in uv.lock
# fail if there's a mismatch with pyproject.toml
RUN uv sync --frozen

# Expose the port the application runs on
EXPOSE 10004

# Command to run the application using python -m uvicorn
CMD ["uv", "run", "src/web_search_agent", "--host", "0.0.0.0", "--port", "10004"]
