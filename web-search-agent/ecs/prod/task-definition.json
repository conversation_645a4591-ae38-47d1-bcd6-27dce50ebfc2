{"family": "math-agent-prod", "taskRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::836929571495:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "math-agent-prod", "image": "836929571495.dkr.ecr.us-east-1.amazonaws.com/math-agent:prod", "memoryReservation": 1024, "memory": 1024, "portMappings": [{"containerPort": 10004, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:836929571495:secret:prod/shared/secrets-HLSQnb:DD_API_KEY::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/prod/math-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "prod"}, {"name": "DD_SERVICE", "value": "math-agent"}, {"name": "DD_VERSION", "value": "1.0"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"web-search-agent\"}]", "com.datadoghq.tags.env": "prod", "com.datadoghq.tags.service": "web-search-agent", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "1024"}