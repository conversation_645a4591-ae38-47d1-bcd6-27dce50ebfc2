# Web Search Agent Tests

This directory contains comprehensive tests for the web-search-agent project.

## Test Structure

### Unit Tests (`tests/unit/`)
- **`test_agent.py`**: Comprehensive unit tests for the web search agent functionality
  - `TestAgentCreation`: Tests for agent creation and configuration
    - Agent instantiation
    - Name, description, and model verification
    - Tool configuration (Google Search)
    - Instruction validation
  - `TestAgentIntegration`: Integration tests for the agent
    - Agent creation without external dependencies
    - Root agent availability

### Integration Tests (`tests/integration/`)
- **`test_evalset.py`**: Tests for evaluation set functionality
  - File structure validation
  - Agent creation without external dependencies
  - Conditional external API testing (skipped when credentials unavailable)
  - Evaluation set validation

## Test Coverage

The tests cover:

1. **Agent Creation**:
   - ✅ Proper LlmAgent instantiation
   - ✅ Correct configuration (name, model, description)
   - ✅ Tool assignment (Google Search)
   - ✅ Instruction validation
   - ✅ Root agent availability

2. **Evaluation Set**:
   - ✅ File structure validation
   - ✅ Required fields presence
   - ✅ Criteria validation
   - ✅ Eval cases validation

3. **Integration**:
   - ✅ Agent creation without external dependencies
   - ✅ Graceful handling of missing credentials
   - ✅ Mocked testing capabilities

## Running Tests

### All Tests
```bash
make test
```

### Unit Tests Only
```bash
make test/unit
```

### Integration Tests Only
```bash
make test/integration
```

### Manual Test Execution
```bash
# Run all tests
PYTHONPATH=. uv run pytest tests/ -v

# Run unit tests only
PYTHONPATH=. uv run pytest tests/unit/ -v

# Run integration tests only
PYTHONPATH=. uv run pytest tests/integration/ -v
```

## Test Environment

The tests are designed to work in environments with or without Google API credentials:

- **With credentials**: Full integration tests run
- **Without credentials**: Tests are skipped gracefully with appropriate messages

This ensures the test suite can run in CI/CD environments without requiring API keys.

## Dependencies

Tests require the following dependencies (already included in `pyproject.toml`):
- `pytest`
- `pytest-asyncio`
- `google-adk[eval]`
- `unittest.mock` (built-in)
