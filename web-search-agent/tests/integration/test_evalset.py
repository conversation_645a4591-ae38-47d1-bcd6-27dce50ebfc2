import logging

import pytest
from dotenv import load_dotenv
from google.adk.evaluation.agent_evaluator import AgentEvaluator

logger = logging.getLogger(__name__)
load_dotenv()


@pytest.mark.asyncio
async def test_with_single_test_file():
    """Test the agent's basic ability via a session file."""
    criteria = {
        "tool_trajectory_avg_score": 0.5,
        "response_match_score": 0.5,
    }
    eval_set = AgentEvaluator._load_eval_set_from_file(
        "src/math_agent/basic.evalset.json", criteria, {}
    )
    await AgentEvaluator.evaluate_eval_set(
        agent_module="src.web_search_agent",
        eval_set=eval_set,
        criteria=criteria,
        num_runs=1,
        agent_name="web_search_agent",
    )
