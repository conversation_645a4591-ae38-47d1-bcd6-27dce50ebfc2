import logging
import pytest
from unittest.mock import patch, Mock
import json
import os

logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_with_single_test_file():
    """Test the agent's basic ability via a session file."""
    # Skip this test if Google Cloud credentials are not available
    # This prevents the test from failing in CI/CD environments without proper setup
    if not os.getenv("GOOGLE_APPLICATION_CREDENTIALS") and not os.getenv("GOOGLE_API_KEY"):
        pytest.skip("Google Cloud credentials not available - skipping external API test")
    
    try:
        from dotenv import load_dotenv
        from google.adk.evaluation.agent_evaluator import AgentEvaluator
        
        load_dotenv()
        
        criteria = {
            "tool_trajectory_avg_score": 0.5,
            "response_match_score": 0.5,
        }
        eval_set = AgentEvaluator._load_eval_set_from_file(
            "src/web_search_agent/basic.evalset.json", criteria, {}
        )
        await AgentEvaluator.evaluate_eval_set(
            agent_module="web_search_agent",
            eval_set=eval_set,
            criteria=criteria,
            num_runs=1,
            agent_name="web_search_agent",
        )
    except Exception as e:
        if "credentials" in str(e).lower() or "authentication" in str(e).lower():
            pytest.skip(f"Authentication failed - skipping external API test: {e}")
        else:
            raise


def test_evalset_file_structure():
    """Test that the evaluation set file has the correct structure."""
    evalset_path = "src/web_search_agent/basic.evalset.json"
    
    assert os.path.exists(evalset_path), f"Eval set file not found: {evalset_path}"
    
    with open(evalset_path, 'r') as f:
        evalset_data = json.load(f)
    
    # Validate required fields
    required_fields = ["eval_set_id", "name", "eval_cases", "criteria"]
    for field in required_fields:
        assert field in evalset_data, f"Missing required field: {field}"
    
    # Validate eval cases
    assert isinstance(evalset_data["eval_cases"], list)
    assert len(evalset_data["eval_cases"]) > 0
    
    # Validate criteria
    required_criteria = ["tool_trajectory_avg_score", "response_match_score"]
    for criterion in required_criteria:
        assert criterion in evalset_data["criteria"], f"Missing required criterion: {criterion}"
        assert isinstance(evalset_data["criteria"][criterion], (int, float))


@pytest.mark.asyncio
async def test_agent_creation_without_external_deps():
    """Test that the agent can be created without external dependencies."""
    with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
        mock_agent_instance = Mock()
        mock_llm_agent.return_value = mock_agent_instance
        
        from web_search_agent.agent import create_agent
        
        agent = create_agent()
        
        assert agent == mock_agent_instance
        mock_llm_agent.assert_called_once()
