import pytest
from unittest.mock import patch, Mock
from google.adk.agents import Llm<PERSON>gent
from google.adk.tools import google_search

from web_search_agent.agent import create_agent


class TestAgentCreation:
    """Tests for agent creation and configuration."""

    def test_create_agent_returns_llm_agent(self):
        """Test that create_agent returns an LlmAgent instance."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            mock_agent_instance = Mock()
            mock_llm_agent.return_value = mock_agent_instance
            
            agent = create_agent()
            
            assert agent == mock_agent_instance
            mock_llm_agent.assert_called_once()

    def test_agent_configuration(self):
        """Test that the agent is configured with correct parameters."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            create_agent()
            
            # Verify LlmAgent was called with correct parameters
            mock_llm_agent.assert_called_once_with(
                name="web_search_agent",
                model="gemini-2.5-flash",
                instruction="Answer questions using Google Search when needed. Always cite sources.",
                description="Professional search assistant with Google Search capabilities",
                tools=[google_search],
            )

    def test_agent_name(self):
        """Test that the agent has the correct name."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            create_agent()
            
            call_args = mock_llm_agent.call_args
            assert call_args[1]['name'] == "web_search_agent"

    def test_agent_model(self):
        """Test that the agent uses the correct model."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            create_agent()
            
            call_args = mock_llm_agent.call_args
            assert call_args[1]['model'] == "gemini-2.5-flash"

    def test_agent_tools(self):
        """Test that the agent has the correct tools."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            create_agent()
            
            call_args = mock_llm_agent.call_args
            assert call_args[1]['tools'] == [google_search]

    def test_agent_description(self):
        """Test that the agent has the correct description."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            create_agent()
            
            call_args = mock_llm_agent.call_args
            assert call_args[1]['description'] == "Professional search assistant with Google Search capabilities"

    def test_agent_instruction(self):
        """Test that the agent has the correct instruction."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            create_agent()
            
            call_args = mock_llm_agent.call_args
            assert call_args[1]['instruction'] == "Answer questions using Google Search when needed. Always cite sources."


class TestAgentIntegration:
    """Integration tests for the agent."""

    @pytest.mark.asyncio
    async def test_agent_creation_without_external_deps(self):
        """Test that the agent can be created without external dependencies."""
        with patch('web_search_agent.agent.LlmAgent') as mock_llm_agent:
            mock_agent_instance = Mock()
            mock_llm_agent.return_value = mock_agent_instance
            
            agent = create_agent()
            
            assert agent == mock_agent_instance
            mock_llm_agent.assert_called_once()

    def test_root_agent_availability(self):
        """Test that root_agent is available in the agent module."""
        from web_search_agent.agent import root_agent
        assert root_agent is not None
        assert isinstance(root_agent, LlmAgent)
